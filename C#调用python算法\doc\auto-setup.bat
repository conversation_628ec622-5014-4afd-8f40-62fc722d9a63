@echo off
chcp 65001 >nul
title 智能烧结配料优化系统 - 自动安装配置

echo.
echo ================================================
echo 🔥 智能烧结配料优化系统 - 自动安装配置
echo ================================================
echo.

echo 正在检查和安装必要的环境...
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限已获取
) else (
    echo ⚠️  需要管理员权限来安装软件
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 检查winget是否可用
winget --version >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Windows Package Manager 可用
    set USE_WINGET=1
) else (
    echo ⚠️  Windows Package Manager 不可用，将使用手动下载方式
    set USE_WINGET=0
)

echo.
echo 📦 开始安装必要组件...
echo.

REM 1. 安装 .NET 8.0 SDK
echo 🔍 检查 .NET 8.0 SDK...
dotnet --version >nul 2>&1
if %errorLevel% == 0 (
    for /f "tokens=1 delims=." %%a in ('dotnet --version') do set DOTNET_MAJOR=%%a
    if !DOTNET_MAJOR! GEQ 8 (
        echo ✅ .NET 8.0 SDK 已安装
    ) else (
        echo 📥 正在安装 .NET 8.0 SDK...
        goto INSTALL_DOTNET
    )
) else (
    echo 📥 正在安装 .NET 8.0 SDK...
    goto INSTALL_DOTNET
)
goto CHECK_PYTHON

:INSTALL_DOTNET
if %USE_WINGET% == 1 (
    echo 使用 winget 安装 .NET 8.0 SDK...
    winget install Microsoft.DotNet.SDK.8 --silent --accept-package-agreements --accept-source-agreements
) else (
    echo 正在下载 .NET 8.0 SDK...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://download.visualstudio.microsoft.com/download/pr/93961dfb-d1e0-49c8-9230-abcba1ebab5a/811ed1eb63d7652325727720edda26a8/dotnet-sdk-8.0.403-win-x64.exe' -OutFile 'dotnet-sdk-installer.exe'}"
    echo 正在安装 .NET 8.0 SDK...
    dotnet-sdk-installer.exe /quiet
    del dotnet-sdk-installer.exe >nul 2>&1
)

REM 刷新环境变量
call refreshenv >nul 2>&1

REM 再次检查
dotnet --version >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ .NET 8.0 SDK 安装成功
) else (
    echo ❌ .NET 8.0 SDK 安装失败，请手动安装
    echo 下载地址: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

:CHECK_PYTHON
REM 2. 检查 Python
echo.
echo 🔍 检查 Python...
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Python 已安装
    python --version
) else (
    echo 📥 正在安装 Python...
    if %USE_WINGET% == 1 (
        winget install Python.Python.3.11 --silent --accept-package-agreements --accept-source-agreements
    ) else (
        echo 正在下载 Python...
        powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.9/python-3.11.9-amd64.exe' -OutFile 'python-installer.exe'}"
        echo 正在安装 Python...
        python-installer.exe /quiet InstallAllUsers=1 PrependPath=1
        del python-installer.exe >nul 2>&1
    )
    
    REM 刷新环境变量
    call refreshenv >nul 2>&1
    
    REM 再次检查
    python --version >nul 2>&1
    if %errorLevel% == 0 (
        echo ✅ Python 安装成功
    ) else (
        echo ❌ Python 安装失败，请手动安装
        echo 下载地址: https://www.python.org/downloads/
        pause
        exit /b 1
    )
)

REM 3. 安装 Python 依赖
echo.
echo 📦 安装 Python 依赖包...
if exist "src\py\requirements.txt" (
    cd src\py
    pip install -r requirements.txt
    if %errorLevel% == 0 (
        echo ✅ Python 依赖安装成功
    ) else (
        echo ❌ Python 依赖安装失败
        pause
        exit /b 1
    )
    cd ..\..
) else (
    echo ⚠️  未找到 requirements.txt 文件
)

REM 4. 构建 C# 项目
echo.
echo 🔨 构建 C# 项目...
if exist "src\csharp" (
    cd src\csharp
    dotnet restore
    if %errorLevel% == 0 (
        echo ✅ 依赖还原成功
        dotnet build --configuration Release
        if %errorLevel% == 0 (
            echo ✅ 项目构建成功
        ) else (
            echo ❌ 项目构建失败
            pause
            exit /b 1
        )
    ) else (
        echo ❌ 依赖还原失败
        pause
        exit /b 1
    )
    cd ..\..
) else (
    echo ⚠️  未找到 C# 项目目录
)

echo.
echo 🎉 环境配置完成！
echo.
echo 现在可以启动系统了...
echo.

REM 询问是否立即启动
set /p choice=是否立即启动系统？(Y/N): 
if /i "%choice%"=="Y" goto START_SYSTEM
if /i "%choice%"=="y" goto START_SYSTEM
goto END

:START_SYSTEM
echo.
echo 🚀 正在启动系统...
echo.

REM 启动 Python 服务
echo 启动 Python 算法服务...
start "Python Service" cmd /k "cd src\py && python optimization_service.py"

REM 等待服务启动
echo 等待服务启动...
timeout /t 5 /nobreak >nul

REM 启动客户端
echo 启动客户端应用...
start "Client App" cmd /k "cd src\csharp\SinterOptimization.Client && dotnet run"

echo.
echo ✅ 系统启动完成！
echo.
echo 📝 使用说明:
echo 1. Python 服务运行在: http://127.0.0.1:5000
echo 2. 客户端应用已启动
echo 3. 如需停止服务，请关闭对应的命令行窗口
echo.

:END
echo 按任意键退出...
pause >nul
exit /b 0
