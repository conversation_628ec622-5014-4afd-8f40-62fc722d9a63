using System.ComponentModel.DataAnnotations;

namespace SinterOptimizationSystem.Core.Models
{
    /// <summary>
    /// 基础实体类，包含通用属性
    /// </summary>
    public abstract class BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人
        /// </summary>
        [MaxLength(50)]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 更新人
        /// </summary>
        [MaxLength(50)]
        public string UpdatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500)]
        public string? Remarks { get; set; }
    }
}
