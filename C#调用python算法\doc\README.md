# 智能烧结配料优化系统

🔥 基于机器学习的智能烧结配料优化系统，提供C#客户端应用和Python算法服务的完整解决方案。

## 🌟 项目特色

- **智能优化算法**: 基于SQP算法的烧结配料优化
- **现代化界面**: WPF + Material Design 的美观客户端
- **实时计算**: HTTP API调用Python算法服务
- **数据可视化**: 配比分布图表和趋势分析
- **历史管理**: 完整的优化历史记录和数据导出

## 📁 项目结构

```
C#调用python算法/
├── src/
│   ├── csharp/                 # C# 客户端项目
│   │   ├── SinterOptimization.Core/      # 核心业务逻辑
│   │   └── SinterOptimization.Client/    # WPF 客户端应用
│   ├── py/                     # Python 算法服务
│   │   ├── optimization_service.py       # Flask API 服务
│   │   ├── sinter_optimization.py        # 优化算法实现
│   │   └── requirements.txt              # Python 依赖
│   └── static/                 # Web 前端页面（参考）
├── build.ps1                  # PowerShell 构建脚本
├── start.bat                  # Windows 启动脚本
└── README.md                  # 项目说明
```

## 🚀 快速开始

### 方式一：使用启动脚本（推荐）

1. **Windows 用户**：双击运行 `start.bat`
2. **PowerShell 用户**：运行 `.\build.ps1 help` 查看所有选项

### 方式二：手动启动

1. **环境要求**
   - Python 3.8+
   - .NET 8.0 SDK
   - Visual Studio 2022 或 VS Code

2. **安装依赖**
   ```bash
   # Python 依赖
   pip install -r src/py/requirements.txt
   
   # C# 依赖
   cd src/csharp
   dotnet restore
   ```

3. **启动 Python 服务**
   ```bash
   cd src/py
   python optimization_service.py
   ```
   服务将在 http://127.0.0.1:5000 运行

4. **启动客户端应用**
   ```bash
   cd src/csharp/SinterOptimization.Client
   dotnet run
   ```

## 💡 系统功能

### 🎯 核心功能

#### 1. 配料优化计算
- 基于目标化学成分（TFe、R、MgO、Al2O3）进行智能配料优化
- 支持多种物料选择和约束条件设置
- 实时显示最优配比方案和预测化学成分

#### 2. 数据可视化
- 配比分布饼图
- 化学成分趋势分析
- 成本效益对比图表

#### 3. 历史记录管理
- 自动保存所有优化记录
- 支持搜索、筛选和排序
- 数据导出（Excel、CSV、JSON）

### 🔧 系统功能

#### 1. API连接管理
- 自动检测Python算法服务状态
- 智能重连和错误处理
- 实时状态监控

#### 2. 参数配置
- 灵活的优化参数设置
- 物料成分和价格管理
- 约束条件自定义

#### 3. 用户体验
- Material Design 现代化界面
- 支持浅色/深色主题切换
- 多语言支持（中文/英文）

## 🛠️ 技术架构

### 前端技术栈
- **框架**: WPF (.NET 8)
- **UI库**: Material Design In XAML
- **图表**: LiveCharts.Wpf
- **MVVM**: CommunityToolkit.Mvvm
- **依赖注入**: Microsoft.Extensions.DependencyInjection

### 后端技术栈
- **算法**: Python + SciPy + NumPy
- **API**: Flask + Flask-CORS
- **优化器**: SQP (Sequential Quadratic Programming)

### 通信协议
- **接口**: RESTful HTTP API
- **数据格式**: JSON
- **认证**: 无需认证（内网使用）

## 📊 算法说明

### 优化目标
最小化目标函数：
```
f(x) = Σ wi * ((actual_i - target_i) / target_i)²
```

其中：
- `wi` 为各化学成分的权重
- `actual_i` 为实际计算值
- `target_i` 为目标值

### 约束条件
- 配比总和 = 100%
- 各物料配比 ≥ 0
- 成本 ≤ 最大成本限制
- 化学成分在合理范围内

### 算法特点
- 收敛速度快
- 全局最优解
- 支持多约束条件
- 数值稳定性好

## 🎨 界面预览

### 主界面
- 现代化的Material Design风格
- 直观的导航栏和状态显示
- 实时API连接状态监控

### 优化界面
- 参数设置区域
- 物料选择面板
- 结果显示和图表可视化

### 历史记录
- 表格形式展示历史数据
- 支持搜索、筛选和导出
- 详细的优化信息查看

## 📝 使用指南

### 1. 基本操作流程
1. 启动Python算法服务
2. 打开客户端应用
3. 设置目标化学成分
4. 选择参与优化的物料
5. 配置权重和约束条件
6. 执行优化算法
7. 查看结果和保存记录

### 2. 参数设置建议
- **TFe含量**: 通常设置在54-56%
- **碱度R**: 根据高炉要求，一般1.8-2.2
- **MgO含量**: 控制在2.0-2.5%
- **Al2O3含量**: 尽量控制在2.0%以下

### 3. 物料选择原则
- 至少选择3种以上物料
- 包含铁矿石、熔剂、燃料
- 考虑成本和供应稳定性

## 🔧 开发指南

### 环境搭建
```bash
# 克隆项目
git clone <repository-url>
cd C#调用python算法

# 安装依赖
pip install -r src/py/requirements.txt
cd src/csharp
dotnet restore

# 构建项目
dotnet build
```

### 添加新功能
1. 在对应的ViewModel中添加业务逻辑
2. 在View中添加UI元素
3. 在Service中添加数据处理逻辑
4. 更新API接口（如需要）

### 调试技巧
- 使用Visual Studio调试C#代码
- 使用Python调试器调试算法
- 查看控制台日志输出
- 使用Postman测试API接口

## 🐛 故障排除

### 常见问题

1. **API连接失败**
   - 检查Python服务是否启动
   - 确认端口5000未被占用
   - 检查防火墙设置

2. **优化计算失败**
   - 验证输入参数范围
   - 检查物料选择是否合理
   - 查看Python服务日志

3. **界面显示异常**
   - 检查.NET运行时版本
   - 确认依赖包完整安装
   - 重启应用程序

### 日志查看
- **客户端日志**: `%APPDATA%/SinterOptimization/logs/`
- **Python服务日志**: 控制台输出
- **API调用日志**: 客户端状态栏显示

## 📈 性能优化

### 算法性能
- 优化迭代次数通常在100-500次
- 计算时间一般在1-5秒
- 内存占用约50-100MB

### 界面性能
- 支持大量历史记录（1000+）
- 图表渲染流畅
- 响应式布局适配

## 🔒 安全考虑

- 本系统设计为内网使用
- 无需用户认证和授权
- 数据存储在本地
- 建议在受信任网络环境中使用

## 🤝 贡献指南

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- **项目维护者**: 开发团队
- **技术支持**: <EMAIL>
- **问题反馈**: [GitHub Issues](https://github.com/your-repo/issues)

## 🙏 致谢

感谢以下开源项目的支持：
- [Material Design In XAML](https://github.com/MaterialDesignInXAML/MaterialDesignInXamlToolkit)
- [LiveCharts](https://github.com/Live-Charts/Live-Charts)
- [CommunityToolkit.Mvvm](https://github.com/CommunityToolkit/dotnet)
- [Flask](https://flask.palletsprojects.com/)
- [SciPy](https://scipy.org/)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
