using System.ComponentModel.DataAnnotations;

namespace SinterOptimizationSystem.Core.Models
{
    /// <summary>
    /// 预测类型枚举
    /// </summary>
    public enum PredictionType
    {
        /// <summary>
        /// 化学成分预测
        /// </summary>
        ChemicalComposition = 1,
        
        /// <summary>
        /// 物理性能预测
        /// </summary>
        PhysicalProperty = 2,
        
        /// <summary>
        /// 质量指标预测
        /// </summary>
        QualityIndex = 3,
        
        /// <summary>
        /// 成本预测
        /// </summary>
        Cost = 4,
        
        /// <summary>
        /// 产量预测
        /// </summary>
        Output = 5
    }

    /// <summary>
    /// 预测算法类型枚举
    /// </summary>
    public enum PredictionAlgorithm
    {
        /// <summary>
        /// 物料平衡法
        /// </summary>
        MaterialBalance = 1,
        
        /// <summary>
        /// 神经网络
        /// </summary>
        NeuralNetwork = 2,
        
        /// <summary>
        /// 线性回归
        /// </summary>
        LinearRegression = 3,
        
        /// <summary>
        /// 支持向量机
        /// </summary>
        SVM = 4,
        
        /// <summary>
        /// 随机森林
        /// </summary>
        RandomForest = 5,
        
        /// <summary>
        /// 混合模型
        /// </summary>
        HybridModel = 6
    }

    /// <summary>
    /// 预测结果模型
    /// </summary>
    public class PredictionResult : BaseEntity
    {
        /// <summary>
        /// 配料方案ID
        /// </summary>
        public int BlendingSchemeId { get; set; }

        /// <summary>
        /// 配料方案对象
        /// </summary>
        public virtual BlendingScheme BlendingScheme { get; set; } = null!;

        /// <summary>
        /// 预测类型
        /// </summary>
        public PredictionType PredictionType { get; set; }

        /// <summary>
        /// 预测算法
        /// </summary>
        public PredictionAlgorithm Algorithm { get; set; }

        /// <summary>
        /// 预测指标名称
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string IndicatorName { get; set; } = string.Empty;

        /// <summary>
        /// 预测值
        /// </summary>
        public decimal PredictedValue { get; set; }

        /// <summary>
        /// 目标值
        /// </summary>
        public decimal? TargetValue { get; set; }

        /// <summary>
        /// 实际值
        /// </summary>
        public decimal? ActualValue { get; set; }

        /// <summary>
        /// 预测误差
        /// </summary>
        public decimal? PredictionError { get; set; }

        /// <summary>
        /// 置信度(%)
        /// </summary>
        [Range(0, 100)]
        public decimal Confidence { get; set; }

        /// <summary>
        /// 预测时间
        /// </summary>
        public DateTime PredictionTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 模型版本
        /// </summary>
        [MaxLength(20)]
        public string? ModelVersion { get; set; }

        /// <summary>
        /// 输入参数(JSON格式)
        /// </summary>
        public string? InputParameters { get; set; }

        /// <summary>
        /// 预测详情(JSON格式)
        /// </summary>
        public string? PredictionDetails { get; set; }

        /// <summary>
        /// 是否异常
        /// </summary>
        public bool IsAbnormal { get; set; } = false;

        /// <summary>
        /// 异常原因
        /// </summary>
        [MaxLength(200)]
        public string? AbnormalReason { get; set; }

        /// <summary>
        /// 建议措施
        /// </summary>
        [MaxLength(500)]
        public string? Recommendations { get; set; }
    }
}
