using Microsoft.Extensions.Logging;
using SinterOptimizationSystem.Core.Interfaces;
using SinterOptimizationSystem.Core.Models;

namespace SinterOptimizationSystem.Services
{
    /// <summary>
    /// 原料管理服务实现
    /// </summary>
    public class MaterialManagementService : IMaterialManagementService
    {
        private readonly ILogger<MaterialManagementService> _logger;
        private readonly List<Material> _materials; // 模拟数据存储，实际应使用数据库
        private readonly List<MaterialComposition> _compositions;
        private readonly List<MaterialPhysicalProperty> _physicalProperties;

        public MaterialManagementService(ILogger<MaterialManagementService> logger)
        {
            _logger = logger;
            _materials = new List<Material>();
            _compositions = new List<MaterialComposition>();
            _physicalProperties = new List<MaterialPhysicalProperty>();
            
            // 初始化示例数据
            InitializeSampleData();
        }

        #region 原料基础管理

        public async Task<IEnumerable<Material>> GetAllMaterialsAsync()
        {
            try
            {
                _logger.LogInformation("获取所有原料信息");
                return await Task.FromResult(_materials.Where(m => !m.IsDeleted));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有原料信息失败");
                throw;
            }
        }

        public async Task<Material?> GetMaterialByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("根据ID获取原料信息: {Id}", id);
                return await Task.FromResult(_materials.FirstOrDefault(m => m.Id == id && !m.IsDeleted));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取原料信息失败: {Id}", id);
                throw;
            }
        }

        public async Task<Material?> GetMaterialByCodeAsync(string code)
        {
            try
            {
                _logger.LogInformation("根据编码获取原料信息: {Code}", code);
                return await Task.FromResult(_materials.FirstOrDefault(m => m.Code == code && !m.IsDeleted));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据编码获取原料信息失败: {Code}", code);
                throw;
            }
        }

        public async Task<IEnumerable<Material>> GetMaterialsByTypeAsync(MaterialType type)
        {
            try
            {
                _logger.LogInformation("根据类型获取原料信息: {Type}", type);
                return await Task.FromResult(_materials.Where(m => m.Type == type && !m.IsDeleted));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据类型获取原料信息失败: {Type}", type);
                throw;
            }
        }

        public async Task<bool> AddMaterialAsync(Material material)
        {
            try
            {
                _logger.LogInformation("添加原料: {Name}", material.Name);
                
                // 检查编码是否重复
                if (_materials.Any(m => m.Code == material.Code && !m.IsDeleted))
                {
                    _logger.LogWarning("原料编码已存在: {Code}", material.Code);
                    return false;
                }

                material.Id = _materials.Count > 0 ? _materials.Max(m => m.Id) + 1 : 1;
                material.CreatedAt = DateTime.Now;
                material.UpdatedAt = DateTime.Now;
                
                _materials.Add(material);
                
                _logger.LogInformation("原料添加成功: {Name}, ID: {Id}", material.Name, material.Id);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加原料失败: {Name}", material.Name);
                return false;
            }
        }

        public async Task<bool> UpdateMaterialAsync(Material material)
        {
            try
            {
                _logger.LogInformation("更新原料: {Id}", material.Id);
                
                var existingMaterial = _materials.FirstOrDefault(m => m.Id == material.Id && !m.IsDeleted);
                if (existingMaterial == null)
                {
                    _logger.LogWarning("原料不存在: {Id}", material.Id);
                    return false;
                }

                // 更新属性
                existingMaterial.Name = material.Name;
                existingMaterial.Code = material.Code;
                existingMaterial.Type = material.Type;
                existingMaterial.Supplier = material.Supplier;
                existingMaterial.Origin = material.Origin;
                existingMaterial.IsActive = material.IsActive;
                existingMaterial.MinRatio = material.MinRatio;
                existingMaterial.MaxRatio = material.MaxRatio;
                existingMaterial.CurrentStock = material.CurrentStock;
                existingMaterial.SafetyStock = material.SafetyStock;
                existingMaterial.Price = material.Price;
                existingMaterial.UpdatedAt = DateTime.Now;
                existingMaterial.UpdatedBy = material.UpdatedBy;
                existingMaterial.Remarks = material.Remarks;

                _logger.LogInformation("原料更新成功: {Id}", material.Id);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新原料失败: {Id}", material.Id);
                return false;
            }
        }

        public async Task<bool> DeleteMaterialAsync(int id)
        {
            try
            {
                _logger.LogInformation("删除原料: {Id}", id);
                
                var material = _materials.FirstOrDefault(m => m.Id == id && !m.IsDeleted);
                if (material == null)
                {
                    _logger.LogWarning("原料不存在: {Id}", id);
                    return false;
                }

                material.IsDeleted = true;
                material.UpdatedAt = DateTime.Now;

                _logger.LogInformation("原料删除成功: {Id}", id);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除原料失败: {Id}", id);
                return false;
            }
        }

        #endregion

        #region 化学成分管理

        public async Task<IEnumerable<MaterialComposition>> GetCurrentCompositionsAsync(int materialId)
        {
            try
            {
                _logger.LogInformation("获取原料当前化学成分: {MaterialId}", materialId);
                return await Task.FromResult(_compositions.Where(c => c.MaterialId == materialId && c.IsCurrent && !c.IsDeleted));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取原料当前化学成分失败: {MaterialId}", materialId);
                throw;
            }
        }

        public async Task<IEnumerable<MaterialComposition>> GetCompositionHistoryAsync(int materialId, DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation("获取原料化学成分历史: {MaterialId}, {StartDate} - {EndDate}", materialId, startDate, endDate);
                return await Task.FromResult(_compositions.Where(c => 
                    c.MaterialId == materialId && 
                    c.TestDate >= startDate && 
                    c.TestDate <= endDate && 
                    !c.IsDeleted));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取原料化学成分历史失败: {MaterialId}", materialId);
                throw;
            }
        }

        public async Task<bool> AddCompositionsAsync(IEnumerable<MaterialComposition> compositions)
        {
            try
            {
                _logger.LogInformation("添加化学成分检测结果");
                
                foreach (var composition in compositions)
                {
                    composition.Id = _compositions.Count > 0 ? _compositions.Max(c => c.Id) + 1 : 1;
                    composition.CreatedAt = DateTime.Now;
                    composition.UpdatedAt = DateTime.Now;
                    _compositions.Add(composition);
                }

                _logger.LogInformation("化学成分检测结果添加成功，数量: {Count}", compositions.Count());
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加化学成分检测结果失败");
                return false;
            }
        }

        public async Task<bool> UpdateCompositionAsync(MaterialComposition composition)
        {
            try
            {
                _logger.LogInformation("更新化学成分: {Id}", composition.Id);
                
                var existingComposition = _compositions.FirstOrDefault(c => c.Id == composition.Id && !c.IsDeleted);
                if (existingComposition == null)
                {
                    _logger.LogWarning("化学成分不存在: {Id}", composition.Id);
                    return false;
                }

                // 更新属性
                existingComposition.CompositionType = composition.CompositionType;
                existingComposition.Content = composition.Content;
                existingComposition.TestDate = composition.TestDate;
                existingComposition.BatchNumber = composition.BatchNumber;
                existingComposition.TestMethod = composition.TestMethod;
                existingComposition.Tester = composition.Tester;
                existingComposition.IsCurrent = composition.IsCurrent;
                existingComposition.StandardDeviation = composition.StandardDeviation;
                existingComposition.Confidence = composition.Confidence;
                existingComposition.UpdatedAt = DateTime.Now;
                existingComposition.UpdatedBy = composition.UpdatedBy;

                _logger.LogInformation("化学成分更新成功: {Id}", composition.Id);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新化学成分失败: {Id}", composition.Id);
                return false;
            }
        }

        public async Task<bool> UpdateCurrentCompositionsAsync(int materialId, IEnumerable<MaterialComposition> compositions)
        {
            try
            {
                _logger.LogInformation("批量更新原料当前成分: {MaterialId}", materialId);
                
                // 将现有的当前成分标记为非当前
                var existingCompositions = _compositions.Where(c => c.MaterialId == materialId && c.IsCurrent).ToList();
                foreach (var existing in existingCompositions)
                {
                    existing.IsCurrent = false;
                    existing.UpdatedAt = DateTime.Now;
                }

                // 添加新的当前成分
                foreach (var composition in compositions)
                {
                    composition.MaterialId = materialId;
                    composition.IsCurrent = true;
                    composition.Id = _compositions.Count > 0 ? _compositions.Max(c => c.Id) + 1 : 1;
                    composition.CreatedAt = DateTime.Now;
                    composition.UpdatedAt = DateTime.Now;
                    _compositions.Add(composition);
                }

                _logger.LogInformation("原料当前成分更新成功: {MaterialId}", materialId);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新原料当前成分失败: {MaterialId}", materialId);
                return false;
            }
        }

        #endregion

        #region 物理性能管理

        public async Task<IEnumerable<MaterialPhysicalProperty>> GetCurrentPhysicalPropertiesAsync(int materialId)
        {
            try
            {
                _logger.LogInformation("获取原料当前物理性能: {MaterialId}", materialId);
                return await Task.FromResult(_physicalProperties.Where(p => p.MaterialId == materialId && p.IsCurrent && !p.IsDeleted));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取原料当前物理性能失败: {MaterialId}", materialId);
                throw;
            }
        }

        public async Task<bool> AddPhysicalPropertiesAsync(IEnumerable<MaterialPhysicalProperty> properties)
        {
            try
            {
                _logger.LogInformation("添加物理性能检测结果");
                
                foreach (var property in properties)
                {
                    property.Id = _physicalProperties.Count > 0 ? _physicalProperties.Max(p => p.Id) + 1 : 1;
                    property.CreatedAt = DateTime.Now;
                    property.UpdatedAt = DateTime.Now;
                    _physicalProperties.Add(property);
                }

                _logger.LogInformation("物理性能检测结果添加成功，数量: {Count}", properties.Count());
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加物理性能检测结果失败");
                return false;
            }
        }

        public async Task<bool> UpdatePhysicalPropertyAsync(MaterialPhysicalProperty property)
        {
            try
            {
                _logger.LogInformation("更新物理性能: {Id}", property.Id);
                
                var existingProperty = _physicalProperties.FirstOrDefault(p => p.Id == property.Id && !p.IsDeleted);
                if (existingProperty == null)
                {
                    _logger.LogWarning("物理性能不存在: {Id}", property.Id);
                    return false;
                }

                // 更新属性
                existingProperty.PropertyType = property.PropertyType;
                existingProperty.Value = property.Value;
                existingProperty.Unit = property.Unit;
                existingProperty.TestDate = property.TestDate;
                existingProperty.BatchNumber = property.BatchNumber;
                existingProperty.TestMethod = property.TestMethod;
                existingProperty.Tester = property.Tester;
                existingProperty.IsCurrent = property.IsCurrent;
                existingProperty.MinValue = property.MinValue;
                existingProperty.MaxValue = property.MaxValue;
                existingProperty.StandardValue = property.StandardValue;
                existingProperty.UpdatedAt = DateTime.Now;
                existingProperty.UpdatedBy = property.UpdatedBy;

                _logger.LogInformation("物理性能更新成功: {Id}", property.Id);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新物理性能失败: {Id}", property.Id);
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化示例数据
        /// </summary>
        private void InitializeSampleData()
        {
            // 添加示例原料数据
            var sampleMaterials = new List<Material>
            {
                new Material { Id = 1, Name = "碱性精粉", Code = "JXJ001", Type = MaterialType.IronOrePowder, Price = 752.21m, IsActive = true },
                new Material { Id = 2, Name = "酸性精粉", Code = "SXJ001", Type = MaterialType.IronOrePowder, Price = 752.21m, IsActive = true },
                new Material { Id = 3, Name = "印粉海娜", Code = "YFH001", Type = MaterialType.IronOrePowder, Price = 832.98m, IsActive = true },
                new Material { Id = 4, Name = "俄罗斯精粉", Code = "ELS001", Type = MaterialType.IronOrePowder, Price = 772.21m, IsActive = true },
                new Material { Id = 5, Name = "高炉返矿", Code = "GLF001", Type = MaterialType.ReturnOre, Price = 550.00m, IsActive = true },
                new Material { Id = 6, Name = "回收料", Code = "HSL001", Type = MaterialType.RecycledMaterial, Price = 100.00m, IsActive = true },
                new Material { Id = 7, Name = "钢渣", Code = "GZ001", Type = MaterialType.Other, Price = 550.00m, IsActive = true },
                new Material { Id = 8, Name = "生石灰", Code = "SSH001", Type = MaterialType.Flux, Price = 219.00m, IsActive = true },
                new Material { Id = 9, Name = "轻烧白云石", Code = "QSBY001", Type = MaterialType.Flux, Price = 183.76m, IsActive = true },
                new Material { Id = 10, Name = "焦粉", Code = "JF001", Type = MaterialType.Fuel, Price = 520.00m, IsActive = true },
                new Material { Id = 11, Name = "澳粉纵横", Code = "AFZH001", Type = MaterialType.IronOrePowder, Price = 832.98m, IsActive = true }
            };

            _materials.AddRange(sampleMaterials);

            // 添加示例化学成分数据
            AddSampleCompositions();
        }

        /// <summary>
        /// 添加示例化学成分数据
        /// </summary>
        private void AddSampleCompositions()
        {
            var compositionData = new Dictionary<int, Dictionary<CompositionType, decimal>>
            {
                [1] = new() { [CompositionType.TFe] = 63.76m, [CompositionType.CaO] = 1.94m, [CompositionType.SiO2] = 4.95m, [CompositionType.MgO] = 1.85m, [CompositionType.Al2O3] = 0.60m, [CompositionType.H2O] = 8.20m, [CompositionType.Ig] = 1.23m },
                [2] = new() { [CompositionType.TFe] = 64.89m, [CompositionType.CaO] = 0.70m, [CompositionType.SiO2] = 6.32m, [CompositionType.MgO] = 0.92m, [CompositionType.Al2O3] = 0.72m, [CompositionType.H2O] = 9.90m, [CompositionType.Ig] = -0.05m },
                [3] = new() { [CompositionType.TFe] = 63.66m, [CompositionType.CaO] = 0.10m, [CompositionType.SiO2] = 4.01m, [CompositionType.MgO] = 0.24m, [CompositionType.Al2O3] = 2.42m, [CompositionType.H2O] = 6.70m, [CompositionType.Ig] = 1.60m },
                [4] = new() { [CompositionType.TFe] = 62.95m, [CompositionType.CaO] = 1.71m, [CompositionType.SiO2] = 4.61m, [CompositionType.MgO] = 3.70m, [CompositionType.Al2O3] = 2.29m, [CompositionType.H2O] = 10.00m, [CompositionType.Ig] = -0.35m },
                [5] = new() { [CompositionType.TFe] = 55.54m, [CompositionType.CaO] = 10.60m, [CompositionType.SiO2] = 5.59m, [CompositionType.MgO] = 2.34m, [CompositionType.Al2O3] = 2.09m, [CompositionType.H2O] = 0.50m, [CompositionType.Ig] = 1.73m },
                [6] = new() { [CompositionType.TFe] = 56.16m, [CompositionType.CaO] = 6.56m, [CompositionType.SiO2] = 6.31m, [CompositionType.MgO] = 2.39m, [CompositionType.Al2O3] = 2.51m, [CompositionType.H2O] = 10.73m, [CompositionType.Ig] = 1.74m },
                [7] = new() { [CompositionType.TFe] = 26.46m, [CompositionType.CaO] = 28.15m, [CompositionType.SiO2] = 15.43m, [CompositionType.MgO] = 2.79m, [CompositionType.Al2O3] = 2.53m, [CompositionType.H2O] = 7.60m, [CompositionType.Ig] = 12.05m },
                [8] = new() { [CompositionType.TFe] = 0.00m, [CompositionType.CaO] = 71.74m, [CompositionType.SiO2] = 3.52m, [CompositionType.MgO] = 2.28m, [CompositionType.Al2O3] = 1.19m, [CompositionType.H2O] = 7.00m, [CompositionType.Ig] = 16.33m },
                [9] = new() { [CompositionType.TFe] = 0.00m, [CompositionType.CaO] = 42.67m, [CompositionType.SiO2] = 5.31m, [CompositionType.MgO] = 26.12m, [CompositionType.Al2O3] = 0.10m, [CompositionType.H2O] = 1.50m, [CompositionType.Ig] = 19.73m },
                [10] = new() { [CompositionType.TFe] = 0.19m, [CompositionType.CaO] = 0.37m, [CompositionType.SiO2] = 8.82m, [CompositionType.MgO] = 0.22m, [CompositionType.Al2O3] = 3.31m, [CompositionType.H2O] = 13.15m, [CompositionType.Ig] = 79.40m },
                [11] = new() { [CompositionType.TFe] = 60.80m, [CompositionType.CaO] = 0.10m, [CompositionType.SiO2] = 4.35m, [CompositionType.MgO] = 0.20m, [CompositionType.Al2O3] = 2.30m, [CompositionType.H2O] = 8.30m, [CompositionType.Ig] = 6.89m }
            };

            foreach (var materialData in compositionData)
            {
                foreach (var composition in materialData.Value)
                {
                    _compositions.Add(new MaterialComposition
                    {
                        Id = _compositions.Count + 1,
                        MaterialId = materialData.Key,
                        CompositionType = composition.Key,
                        Content = composition.Value,
                        TestDate = DateTime.Now.AddDays(-1),
                        IsCurrent = true,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    });
                }
            }
        }

        #endregion

        #region 其他接口实现（简化版本）

        public async Task<decimal> CalculateCostPerformanceRatioAsync(int materialId)
        {
            // 简化实现，实际应根据具体业务逻辑计算
            var material = await GetMaterialByIdAsync(materialId);
            if (material == null) return 0;

            var compositions = await GetCurrentCompositionsAsync(materialId);
            var tfeComposition = compositions.FirstOrDefault(c => c.CompositionType == CompositionType.TFe);
            
            if (tfeComposition != null && material.Price > 0)
            {
                return tfeComposition.Content / material.Price * 1000; // 性价比 = TFe含量 / 价格 * 1000
            }

            return 0;
        }

        public async Task<IEnumerable<(Material Material, decimal Ratio)>> GetCostPerformanceRankingAsync(MaterialType? materialType = null)
        {
            var materials = materialType.HasValue 
                ? await GetMaterialsByTypeAsync(materialType.Value)
                : await GetAllMaterialsAsync();

            var rankings = new List<(Material Material, decimal Ratio)>();

            foreach (var material in materials)
            {
                var ratio = await CalculateCostPerformanceRatioAsync(material.Id);
                rankings.Add((material, ratio));
            }

            return rankings.OrderByDescending(r => r.Ratio);
        }

        public async Task<IEnumerable<Material>> GetPurchaseRecommendationsAsync(Dictionary<CompositionType, decimal> targetComposition, decimal budgetLimit)
        {
            // 简化实现
            var materials = await GetAllMaterialsAsync();
            return materials.Where(m => m.Price <= budgetLimit && m.IsActive);
        }

        public async Task<bool> UpdateStockAsync(int materialId, decimal quantity, string operationType)
        {
            var material = await GetMaterialByIdAsync(materialId);
            if (material == null) return false;

            if (operationType == "入库")
                material.CurrentStock += quantity;
            else if (operationType == "出库")
                material.CurrentStock -= quantity;

            material.UpdatedAt = DateTime.Now;
            return true;
        }

        public async Task<IEnumerable<Material>> GetLowStockMaterialsAsync()
        {
            var materials = await GetAllMaterialsAsync();
            return materials.Where(m => m.CurrentStock <= m.SafetyStock);
        }

        public async Task<int> PredictStockConsumptionAsync(int materialId, decimal consumptionRate)
        {
            var material = await GetMaterialByIdAsync(materialId);
            if (material == null || consumptionRate <= 0) return 0;

            return (int)(material.CurrentStock / consumptionRate);
        }

        public async Task<(bool IsValid, string[] Errors)> ValidateCompositionsAsync(IEnumerable<MaterialComposition> compositions)
        {
            var errors = new List<string>();

            foreach (var composition in compositions)
            {
                if (composition.Content < 0 || composition.Content > 100)
                    errors.Add($"成分 {composition.CompositionType} 含量超出范围 (0-100%)");
            }

            return await Task.FromResult((errors.Count == 0, errors.ToArray()));
        }

        public async Task<(decimal Trend, decimal Variance)> AnalyzeCompositionTrendAsync(int materialId, CompositionType compositionType, int days = 30)
        {
            var startDate = DateTime.Now.AddDays(-days);
            var compositions = await GetCompositionHistoryAsync(materialId, startDate, DateTime.Now);
            var targetCompositions = compositions.Where(c => c.CompositionType == compositionType).OrderBy(c => c.TestDate).ToList();

            if (targetCompositions.Count < 2)
                return (0, 0);

            var values = targetCompositions.Select(c => c.Content).ToArray();
            var trend = (values.Last() - values.First()) / days; // 简化的趋势计算
            var variance = CalculateVariance(values);

            return (trend, variance);
        }

        public async Task<IEnumerable<MaterialComposition>> DetectAbnormalCompositionsAsync(int materialId)
        {
            var compositions = await GetCurrentCompositionsAsync(materialId);
            var abnormalCompositions = new List<MaterialComposition>();

            // 简化的异常检测逻辑
            foreach (var composition in compositions)
            {
                if (composition.Content < 0 || composition.Content > 100)
                    abnormalCompositions.Add(composition);
            }

            return abnormalCompositions;
        }

        private decimal CalculateVariance(decimal[] values)
        {
            if (values.Length == 0) return 0;

            var mean = values.Average();
            var variance = values.Sum(v => (v - mean) * (v - mean)) / values.Length;
            return variance;
        }

        #endregion
    }
}
