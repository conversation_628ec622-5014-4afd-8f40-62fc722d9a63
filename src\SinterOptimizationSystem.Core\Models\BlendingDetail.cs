using System.ComponentModel.DataAnnotations;

namespace SinterOptimizationSystem.Core.Models
{
    /// <summary>
    /// 配料明细模型
    /// </summary>
    public class BlendingDetail : BaseEntity
    {
        /// <summary>
        /// 配料方案ID
        /// </summary>
        public int BlendingSchemeId { get; set; }

        /// <summary>
        /// 配料方案对象
        /// </summary>
        public virtual BlendingScheme BlendingScheme { get; set; } = null!;

        /// <summary>
        /// 原料ID
        /// </summary>
        public int MaterialId { get; set; }

        /// <summary>
        /// 原料对象
        /// </summary>
        public virtual Material Material { get; set; } = null!;

        /// <summary>
        /// 湿配比(%)
        /// </summary>
        [Range(0, 100)]
        public decimal WetRatio { get; set; }

        /// <summary>
        /// 干配比(%)
        /// </summary>
        [Range(0, 100)]
        public decimal DryRatio { get; set; }

        /// <summary>
        /// 下料量(吨/小时)
        /// </summary>
        public decimal FeedingRate { get; set; }

        /// <summary>
        /// 料仓编号
        /// </summary>
        [MaxLength(20)]
        public string? BinNumber { get; set; }

        /// <summary>
        /// 下料口编号
        /// </summary>
        [MaxLength(20)]
        public string? FeedingPortNumber { get; set; }

        /// <summary>
        /// 是否为调整仓
        /// </summary>
        public bool IsAdjustmentBin { get; set; } = false;

        /// <summary>
        /// 最小配比限制(%)
        /// </summary>
        public decimal MinRatio { get; set; } = 0;

        /// <summary>
        /// 最大配比限制(%)
        /// </summary>
        public decimal MaxRatio { get; set; } = 100;

        /// <summary>
        /// 当前库存量(吨)
        /// </summary>
        public decimal CurrentStock { get; set; }

        /// <summary>
        /// 预计消耗时间(小时)
        /// </summary>
        public decimal? EstimatedConsumptionTime { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; } = 1;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 成本贡献(元/吨)
        /// </summary>
        public decimal CostContribution { get; set; }

        /// <summary>
        /// 质量影响系数
        /// </summary>
        public decimal QualityImpactFactor { get; set; } = 1.0m;
    }
}
