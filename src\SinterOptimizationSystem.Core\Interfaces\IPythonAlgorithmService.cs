namespace SinterOptimizationSystem.Core.Interfaces
{
    /// <summary>
    /// Python算法输入参数
    /// </summary>
    public class PythonAlgorithmInput
    {
        /// <summary>
        /// 原料名称列表
        /// </summary>
        public List<string> MaterialNames { get; set; } = new();

        /// <summary>
        /// 原料化学成分矩阵 [原料数量 x 成分数量]
        /// TFe, CaO, SiO2, MgO, Al2O3, H2O, Ig, Price
        /// </summary>
        public decimal[,] MaterialsMatrix { get; set; } = new decimal[0,0];

        /// <summary>
        /// 人工初选物料标志
        /// </summary>
        public bool[] ManualSelection { get; set; } = Array.Empty<bool>();

        /// <summary>
        /// 目标值
        /// </summary>
        public Dictionary<string, decimal> Targets { get; set; } = new();

        /// <summary>
        /// 约束范围
        /// </summary>
        public Dictionary<string, decimal[]> Ranges { get; set; } = new();

        /// <summary>
        /// 优化权重
        /// </summary>
        public Dictionary<string, decimal> Weights { get; set; } = new();

        /// <summary>
        /// 初始配比
        /// </summary>
        public decimal[] InitialRatios { get; set; } = Array.Empty<decimal>();

        /// <summary>
        /// 配比边界条件 [原料数量 x 2] (最小值, 最大值)
        /// </summary>
        public decimal[,] BoundsMatrix { get; set; } = new decimal[0,0];

        /// <summary>
        /// 最小湿配比阈值
        /// </summary>
        public decimal MinWetRatioThreshold { get; set; } = 2.0m;

        /// <summary>
        /// 最大迭代次数
        /// </summary>
        public int MaxIterations { get; set; } = 500;

        /// <summary>
        /// 收敛容差
        /// </summary>
        public decimal Tolerance { get; set; } = 1e-7m;
    }

    /// <summary>
    /// Python算法输出结果
    /// </summary>
    public class PythonAlgorithmOutput
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 最优配比向量
        /// </summary>
        public decimal[] OptimalRatios { get; set; } = Array.Empty<decimal>();

        /// <summary>
        /// 最终属性值
        /// </summary>
        public Dictionary<string, decimal> FinalProperties { get; set; } = new();

        /// <summary>
        /// 迭代次数
        /// </summary>
        public int IterationCount { get; set; }

        /// <summary>
        /// 目标函数值
        /// </summary>
        public decimal ObjectiveValue { get; set; }

        /// <summary>
        /// 约束满足情况
        /// </summary>
        public Dictionary<string, bool> ConstraintsSatisfied { get; set; } = new();

        /// <summary>
        /// 优化历史记录
        /// </summary>
        public decimal[] ObjectiveHistory { get; set; } = Array.Empty<decimal>();

        /// <summary>
        /// 计算耗时(毫秒)
        /// </summary>
        public long CalculationTime { get; set; }

        /// <summary>
        /// 终止原因
        /// </summary>
        public string? TerminationReason { get; set; }

        /// <summary>
        /// 警告信息
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// 详细结果(JSON格式)
        /// </summary>
        public string? DetailedResults { get; set; }
    }

    /// <summary>
    /// Python算法服务接口
    /// </summary>
    public interface IPythonAlgorithmService
    {
        #region 算法调用

        /// <summary>
        /// 执行SQP优化算法
        /// </summary>
        /// <param name="input">输入参数</param>
        /// <returns>优化结果</returns>
        Task<PythonAlgorithmOutput> RunSQPOptimizationAsync(PythonAlgorithmInput input);

        /// <summary>
        /// 执行线性规划算法
        /// </summary>
        /// <param name="input">输入参数</param>
        /// <returns>优化结果</returns>
        Task<PythonAlgorithmOutput> RunLinearProgrammingAsync(PythonAlgorithmInput input);

        /// <summary>
        /// 执行遗传算法
        /// </summary>
        /// <param name="input">输入参数</param>
        /// <returns>优化结果</returns>
        Task<PythonAlgorithmOutput> RunGeneticAlgorithmAsync(PythonAlgorithmInput input);

        /// <summary>
        /// 执行粒子群算法
        /// </summary>
        /// <param name="input">输入参数</param>
        /// <returns>优化结果</returns>
        Task<PythonAlgorithmOutput> RunParticleSwarmOptimizationAsync(PythonAlgorithmInput input);

        #endregion

        #region 算法管理

        /// <summary>
        /// 检查Python环境
        /// </summary>
        /// <returns>环境检查结果</returns>
        Task<(bool IsAvailable, string Version, string[] MissingPackages)> CheckPythonEnvironmentAsync();

        /// <summary>
        /// 安装Python依赖包
        /// </summary>
        /// <param name="packages">包列表</param>
        /// <returns>安装结果</returns>
        Task<bool> InstallPythonPackagesAsync(string[] packages);

        /// <summary>
        /// 更新算法脚本
        /// </summary>
        /// <param name="scriptPath">脚本路径</param>
        /// <param name="scriptContent">脚本内容</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateAlgorithmScriptAsync(string scriptPath, string scriptContent);

        /// <summary>
        /// 获取算法版本信息
        /// </summary>
        /// <returns>版本信息</returns>
        Task<Dictionary<string, string>> GetAlgorithmVersionsAsync();

        #endregion

        #region 性能监控

        /// <summary>
        /// 获取算法性能统计
        /// </summary>
        /// <param name="algorithmType">算法类型</param>
        /// <param name="days">统计天数</param>
        /// <returns>性能统计</returns>
        Task<Dictionary<string, object>> GetAlgorithmPerformanceStatsAsync(string algorithmType, int days = 7);

        /// <summary>
        /// 记录算法执行日志
        /// </summary>
        /// <param name="algorithmType">算法类型</param>
        /// <param name="input">输入参数</param>
        /// <param name="output">输出结果</param>
        /// <param name="executionTime">执行时间</param>
        /// <returns>记录结果</returns>
        Task<bool> LogAlgorithmExecutionAsync(string algorithmType, PythonAlgorithmInput input, PythonAlgorithmOutput output, long executionTime);

        #endregion

        #region 数据转换

        /// <summary>
        /// 将C#数据转换为Python输入格式
        /// </summary>
        /// <param name="materials">原料列表</param>
        /// <param name="request">计算请求</param>
        /// <returns>Python输入参数</returns>
        Task<PythonAlgorithmInput> ConvertToPythonInputAsync(IEnumerable<Models.Material> materials, IBlendingCalculationService.BlendingCalculationRequest request);

        /// <summary>
        /// 将Python输出转换为C#结果格式
        /// </summary>
        /// <param name="pythonOutput">Python输出</param>
        /// <param name="materials">原料列表</param>
        /// <returns>配料计算结果</returns>
        Task<IBlendingCalculationService.BlendingCalculationResult> ConvertFromPythonOutputAsync(PythonAlgorithmOutput pythonOutput, IEnumerable<Models.Material> materials);

        #endregion

        #region 缓存管理

        /// <summary>
        /// 缓存计算结果
        /// </summary>
        /// <param name="inputHash">输入参数哈希</param>
        /// <param name="output">输出结果</param>
        /// <param name="expireMinutes">过期时间(分钟)</param>
        /// <returns>缓存结果</returns>
        Task<bool> CacheCalculationResultAsync(string inputHash, PythonAlgorithmOutput output, int expireMinutes = 60);

        /// <summary>
        /// 获取缓存的计算结果
        /// </summary>
        /// <param name="inputHash">输入参数哈希</param>
        /// <returns>缓存的结果</returns>
        Task<PythonAlgorithmOutput?> GetCachedCalculationResultAsync(string inputHash);

        /// <summary>
        /// 清理过期缓存
        /// </summary>
        /// <returns>清理结果</returns>
        Task<bool> ClearExpiredCacheAsync();

        #endregion
    }
}
