using System.ComponentModel.DataAnnotations;

namespace SinterOptimizationSystem.Core.Models
{
    /// <summary>
    /// 物理性能类型枚举
    /// </summary>
    public enum PhysicalPropertyType
    {
        /// <summary>
        /// 粒度分布
        /// </summary>
        ParticleSize = 1,
        
        /// <summary>
        /// 比表面积
        /// </summary>
        SpecificSurfaceArea = 2,
        
        /// <summary>
        /// 密度
        /// </summary>
        Density = 3,
        
        /// <summary>
        /// 孔隙率
        /// </summary>
        Porosity = 4,
        
        /// <summary>
        /// 透气性
        /// </summary>
        Permeability = 5,
        
        /// <summary>
        /// 热值
        /// </summary>
        CalorificValue = 6,
        
        /// <summary>
        /// 灰分
        /// </summary>
        AshContent = 7,
        
        /// <summary>
        /// 挥发分
        /// </summary>
        VolatileMatter = 8,
        
        /// <summary>
        /// 固定碳
        /// </summary>
        FixedCarbon = 9,
        
        /// <summary>
        /// 软化温度
        /// </summary>
        SofteningTemperature = 10
    }

    /// <summary>
    /// 原料物理性能模型
    /// </summary>
    public class MaterialPhysicalProperty : BaseEntity
    {
        /// <summary>
        /// 原料ID
        /// </summary>
        public int MaterialId { get; set; }

        /// <summary>
        /// 原料对象
        /// </summary>
        public virtual Material Material { get; set; } = null!;

        /// <summary>
        /// 物理性能类型
        /// </summary>
        public PhysicalPropertyType PropertyType { get; set; }

        /// <summary>
        /// 性能值
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [MaxLength(20)]
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime TestDate { get; set; } = DateTime.Now;

        /// <summary>
        /// 检测批次号
        /// </summary>
        [MaxLength(50)]
        public string? BatchNumber { get; set; }

        /// <summary>
        /// 检测方法
        /// </summary>
        [MaxLength(100)]
        public string? TestMethod { get; set; }

        /// <summary>
        /// 检测人员
        /// </summary>
        [MaxLength(50)]
        public string? Tester { get; set; }

        /// <summary>
        /// 是否为当前有效值
        /// </summary>
        public bool IsCurrent { get; set; } = true;

        /// <summary>
        /// 最小值
        /// </summary>
        public decimal? MinValue { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public decimal? MaxValue { get; set; }

        /// <summary>
        /// 标准值
        /// </summary>
        public decimal? StandardValue { get; set; }
    }
}
