# 智能烧结配料优化系统 - 部署指南

## 📋 部署前准备

### 系统要求

#### 硬件要求
- **CPU**: Intel i5 或 AMD Ryzen 5 及以上
- **内存**: 8GB RAM 或更多
- **存储**: 至少 2GB 可用空间
- **网络**: 内网连接（用于API通信）

#### 软件要求
- **操作系统**: Windows 10/11 (x64)
- **.NET Runtime**: .NET 8.0 Desktop Runtime
- **Python**: Python 3.8+ (推荐 3.9 或 3.10)
- **Visual C++ Redistributable**: 最新版本

### 环境检查清单

- [ ] Windows 10/11 x64 系统
- [ ] .NET 8.0 Desktop Runtime 已安装
- [ ] Python 3.8+ 已安装并配置环境变量
- [ ] pip 包管理器可用
- [ ] 防火墙允许端口 5000 通信
- [ ] 足够的磁盘空间（至少 2GB）

## 🚀 快速部署

### 方式一：使用预编译版本（推荐）

1. **下载发布包**
   ```
   下载 SinterOptimization-v1.0.0-win-x64.zip
   解压到目标目录，如 C:\SinterOptimization\
   ```

2. **安装Python依赖**
   ```bash
   cd C:\SinterOptimization\py
   pip install -r requirements.txt
   ```

3. **启动系统**
   ```bash
   # 方式1：使用启动脚本
   双击运行 start.bat
   
   # 方式2：手动启动
   # 终端1：启动Python服务
   cd py
   python optimization_service.py
   
   # 终端2：启动客户端
   cd client
   SinterOptimization.Client.exe
   ```

### 方式二：从源码构建

1. **克隆代码库**
   ```bash
   git clone <repository-url>
   cd C#调用python算法
   ```

2. **构建项目**
   ```bash
   # 使用PowerShell脚本
   .\build.ps1 build -Clean
   
   # 或手动构建
   cd src\csharp
   dotnet restore
   dotnet build --configuration Release
   ```

3. **安装Python依赖**
   ```bash
   cd src\py
   pip install -r requirements.txt
   ```

4. **运行系统**
   ```bash
   .\build.ps1 run-python    # 启动Python服务
   .\build.ps1 run-client    # 启动客户端
   ```

## 🔧 详细配置

### Python环境配置

1. **创建虚拟环境（推荐）**
   ```bash
   python -m venv venv
   venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **验证依赖安装**
   ```bash
   python -c "import numpy, scipy, flask; print('依赖安装成功')"
   ```

3. **配置环境变量**
   ```bash
   set PYTHONIOENCODING=utf-8
   set FLASK_ENV=production
   ```

### 客户端配置

1. **编辑配置文件**
   ```json
   // appsettings.json
   {
     "ApiClient": {
       "BaseUrl": "http://127.0.0.1:5000",
       "TimeoutSeconds": 60
     },
     "PythonEngine": {
       "PythonPath": "C:\\Python39\\python.exe",
       "ScriptPath": "..\\..\\py\\optimization_service.py"
     }
   }
   ```

2. **数据库初始化**
   ```
   首次运行时会自动创建数据目录：
   %APPDATA%\SinterOptimization\
   ```

### 网络配置

1. **防火墙设置**
   ```
   允许入站连接：端口 5000 (TCP)
   允许出站连接：端口 5000 (TCP)
   ```

2. **代理设置**
   ```
   如果使用代理，需要配置：
   HTTP_PROXY=http://proxy:port
   HTTPS_PROXY=http://proxy:port
   ```

## 🏢 企业部署

### 集中式部署

1. **服务器部署Python API**
   ```bash
   # 在服务器上部署Python服务
   cd /opt/sinter-optimization/py
   python optimization_service.py --host 0.0.0.0 --port 5000
   ```

2. **客户端配置**
   ```json
   {
     "ApiClient": {
       "BaseUrl": "http://server-ip:5000"
     }
   }
   ```

### 容器化部署

1. **Docker部署Python服务**
   ```dockerfile
   FROM python:3.9-slim
   WORKDIR /app
   COPY py/ .
   RUN pip install -r requirements.txt
   EXPOSE 5000
   CMD ["python", "optimization_service.py"]
   ```

2. **构建和运行**
   ```bash
   docker build -t sinter-optimization-api .
   docker run -d -p 5000:5000 sinter-optimization-api
   ```

### 负载均衡

1. **Nginx配置**
   ```nginx
   upstream sinter_api {
       server 127.0.0.1:5001;
       server 127.0.0.1:5002;
       server 127.0.0.1:5003;
   }
   
   server {
       listen 5000;
       location / {
           proxy_pass http://sinter_api;
       }
   }
   ```

## 🔒 安全配置

### 网络安全

1. **HTTPS配置**
   ```python
   # 在production环境启用HTTPS
   app.run(host='0.0.0.0', port=5000, ssl_context='adhoc')
   ```

2. **API认证**
   ```python
   # 添加API密钥认证
   @app.before_request
   def check_api_key():
       api_key = request.headers.get('X-API-Key')
       if api_key != 'your-secret-key':
           abort(401)
   ```

### 数据安全

1. **数据加密**
   ```
   启用数据库加密
   配置文件敏感信息加密
   ```

2. **访问控制**
   ```
   限制文件系统访问权限
   配置用户角色和权限
   ```

## 📊 监控和维护

### 日志配置

1. **Python服务日志**
   ```python
   import logging
   logging.basicConfig(
       level=logging.INFO,
       format='%(asctime)s - %(levelname)s - %(message)s',
       handlers=[
           logging.FileHandler('logs/api.log'),
           logging.StreamHandler()
       ]
   )
   ```

2. **客户端日志**
   ```json
   {
     "Logging": {
       "LogLevel": {
         "Default": "Information"
       }
     }
   }
   ```

### 性能监控

1. **系统资源监控**
   ```
   CPU使用率 < 80%
   内存使用率 < 70%
   磁盘空间 > 1GB
   ```

2. **API性能监控**
   ```
   响应时间 < 5秒
   成功率 > 95%
   并发连接数监控
   ```

### 备份策略

1. **数据备份**
   ```bash
   # 每日备份脚本
   xcopy "%APPDATA%\SinterOptimization" "D:\Backup\%date%" /E /I
   ```

2. **配置备份**
   ```bash
   # 备份配置文件
   copy appsettings.json backup\appsettings_%date%.json
   ```

## 🐛 故障排除

### 常见问题

1. **Python服务启动失败**
   ```
   问题：ModuleNotFoundError
   解决：pip install -r requirements.txt
   
   问题：端口被占用
   解决：netstat -ano | findstr :5000
         taskkill /PID <PID> /F
   ```

2. **客户端连接失败**
   ```
   问题：API连接超时
   解决：检查防火墙设置
         验证服务器地址和端口
   
   问题：.NET运行时错误
   解决：安装.NET 8.0 Desktop Runtime
   ```

3. **优化计算失败**
   ```
   问题：参数验证失败
   解决：检查输入参数范围
   
   问题：算法收敛失败
   解决：调整优化参数和约束条件
   ```

### 诊断工具

1. **网络连接测试**
   ```bash
   # 测试API连接
   curl http://127.0.0.1:5000/health
   
   # 测试端口连通性
   telnet 127.0.0.1 5000
   ```

2. **日志分析**
   ```bash
   # 查看Python服务日志
   tail -f logs/api.log
   
   # 查看客户端日志
   type "%APPDATA%\SinterOptimization\logs\app.log"
   ```

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **问题反馈**: [GitHub Issues](https://github.com/your-repo/issues)
- **文档中心**: [Wiki页面](https://github.com/your-repo/wiki)

### 支持时间
- **工作日**: 9:00 - 18:00
- **紧急问题**: 24小时响应
- **版本更新**: 每月第一个周五

---

📝 **注意**: 本部署指南适用于 v1.0.0 版本，不同版本可能有所差异。
