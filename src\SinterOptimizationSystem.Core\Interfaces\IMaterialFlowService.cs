using SinterOptimizationSystem.Core.Models;

namespace SinterOptimizationSystem.Core.Interfaces
{
    /// <summary>
    /// 料流跟踪服务接口
    /// </summary>
    public interface IMaterialFlowService
    {
        #region 料流基础管理

        /// <summary>
        /// 创建新的料流批次
        /// </summary>
        /// <param name="materialFlow">料流信息</param>
        /// <returns>创建结果</returns>
        Task<bool> CreateMaterialFlowAsync(MaterialFlow materialFlow);

        /// <summary>
        /// 获取料流信息
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <returns>料流信息</returns>
        Task<MaterialFlow?> GetMaterialFlowAsync(string batchNumber);

        /// <summary>
        /// 获取料仓当前料流
        /// </summary>
        /// <param name="binNumber">料仓编号</param>
        /// <returns>当前料流列表</returns>
        Task<IEnumerable<MaterialFlow>> GetCurrentFlowsInBinAsync(string binNumber);

        /// <summary>
        /// 获取原料的所有料流记录
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>料流记录列表</returns>
        Task<IEnumerable<MaterialFlow>> GetMaterialFlowHistoryAsync(int materialId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 更新料流状态
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <param name="status">新状态</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateFlowStatusAsync(string batchNumber, MaterialFlowStatus status);

        #endregion

        #region 灌仓管理

        /// <summary>
        /// 开始灌仓
        /// </summary>
        /// <param name="binNumber">料仓编号</param>
        /// <param name="materialId">原料ID</param>
        /// <param name="batchWeight">批重</param>
        /// <param name="supplierBatchNumber">供应商批次号</param>
        /// <returns>灌仓结果</returns>
        Task<(bool IsSuccess, string BatchNumber)> StartBinFillingAsync(string binNumber, int materialId, decimal batchWeight, string? supplierBatchNumber = null);

        /// <summary>
        /// 完成灌仓
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <param name="actualWeight">实际重量</param>
        /// <returns>完成结果</returns>
        Task<bool> CompleteBinFillingAsync(string batchNumber, decimal actualWeight);

        /// <summary>
        /// 获取灌仓统计信息
        /// </summary>
        /// <param name="binNumber">料仓编号</param>
        /// <param name="date">日期</param>
        /// <returns>灌仓统计</returns>
        Task<(int FillingCount, decimal TotalWeight, TimeSpan TotalTime)> GetFillingStatsAsync(string binNumber, DateTime date);

        #endregion

        #region 下料批次跟踪

        /// <summary>
        /// 开始下料
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <param name="feedingPortNumber">下料口编号</param>
        /// <param name="targetRate">目标下料速率</param>
        /// <returns>开始结果</returns>
        Task<bool> StartFeedingAsync(string batchNumber, string feedingPortNumber, decimal targetRate);

        /// <summary>
        /// 记录下料操作
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <param name="operationType">操作类型</param>
        /// <param name="weightChange">重量变化</param>
        /// <param name="operationReason">操作原因</param>
        /// <returns>记录结果</returns>
        Task<bool> RecordFeedingOperationAsync(string batchNumber, FlowOperationType operationType, decimal weightChange, string? operationReason = null);

        /// <summary>
        /// 完成下料
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <returns>完成结果</returns>
        Task<bool> CompleteFeedingAsync(string batchNumber);

        /// <summary>
        /// 获取下料进度
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <returns>下料进度信息</returns>
        Task<(decimal Progress, decimal RemainingWeight, TimeSpan EstimatedTime)> GetFeedingProgressAsync(string batchNumber);

        #endregion

        #region 动态定位

        /// <summary>
        /// 定位当前使用的料批
        /// </summary>
        /// <param name="binNumber">料仓编号</param>
        /// <returns>当前料批信息</returns>
        Task<MaterialFlow?> LocateCurrentBatchAsync(string binNumber);

        /// <summary>
        /// 预测料批消耗时间
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <param name="currentRate">当前下料速率</param>
        /// <returns>预计消耗时间</returns>
        Task<DateTime?> PredictBatchCompletionTimeAsync(string batchNumber, decimal currentRate);

        /// <summary>
        /// 获取料仓仓位信息
        /// </summary>
        /// <param name="binNumber">料仓编号</param>
        /// <returns>仓位信息</returns>
        Task<(decimal Level, decimal Capacity, decimal UsedCapacity)> GetBinLevelInfoAsync(string binNumber);

        /// <summary>
        /// 更新仓位高度
        /// </summary>
        /// <param name="binNumber">料仓编号</param>
        /// <param name="level">仓位高度</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateBinLevelAsync(string binNumber, decimal level);

        #endregion

        #region 成分匹配

        /// <summary>
        /// 匹配料批成分
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <param name="compositions">化学成分</param>
        /// <returns>匹配结果</returns>
        Task<bool> MatchBatchCompositionAsync(string batchNumber, IEnumerable<MaterialComposition> compositions);

        /// <summary>
        /// 获取料批成分信息
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <returns>成分信息</returns>
        Task<IEnumerable<MaterialComposition>> GetBatchCompositionAsync(string batchNumber);

        /// <summary>
        /// 分析成分偏差
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <param name="standardCompositions">标准成分</param>
        /// <returns>偏差分析结果</returns>
        Task<Dictionary<CompositionType, decimal>> AnalyzeCompositionDeviationAsync(string batchNumber, IEnumerable<MaterialComposition> standardCompositions);

        #endregion

        #region 异常处理

        /// <summary>
        /// 检测料流异常
        /// </summary>
        /// <param name="binNumber">料仓编号</param>
        /// <returns>异常信息列表</returns>
        Task<List<string>> DetectFlowAnomaliesAsync(string binNumber);

        /// <summary>
        /// 处理下料偏差
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <param name="targetRate">目标速率</param>
        /// <param name="actualRate">实际速率</param>
        /// <returns>处理建议</returns>
        Task<string> HandleFeedingDeviationAsync(string batchNumber, decimal targetRate, decimal actualRate);

        /// <summary>
        /// 生成料流报警
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <param name="alarmType">报警类型</param>
        /// <param name="message">报警消息</param>
        /// <returns>报警结果</returns>
        Task<bool> GenerateFlowAlarmAsync(string batchNumber, string alarmType, string message);

        #endregion

        #region 统计分析

        /// <summary>
        /// 获取料流统计信息
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>统计信息</returns>
        Task<Dictionary<string, object>> GetFlowStatisticsAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 分析料流效率
        /// </summary>
        /// <param name="binNumber">料仓编号</param>
        /// <param name="days">分析天数</param>
        /// <returns>效率分析结果</returns>
        Task<(decimal Efficiency, decimal AverageRate, decimal Utilization)> AnalyzeFlowEfficiencyAsync(string binNumber, int days = 7);

        /// <summary>
        /// 获取料流趋势
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <param name="days">分析天数</param>
        /// <returns>趋势数据</returns>
        Task<Dictionary<DateTime, decimal>> GetFlowTrendAsync(int materialId, int days = 30);

        #endregion

        #region 质量跟踪

        /// <summary>
        /// 记录质量检测结果
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <param name="qualityParameters">质量参数</param>
        /// <returns>记录结果</returns>
        Task<bool> RecordQualityTestAsync(string batchNumber, Dictionary<string, decimal> qualityParameters);

        /// <summary>
        /// 获取质量跟踪记录
        /// </summary>
        /// <param name="batchNumber">批次号</param>
        /// <returns>质量记录</returns>
        Task<Dictionary<string, decimal>> GetQualityTrackingAsync(string batchNumber);

        /// <summary>
        /// 分析质量稳定性
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <param name="days">分析天数</param>
        /// <returns>稳定性分析结果</returns>
        Task<Dictionary<string, (decimal Mean, decimal StdDev, decimal Stability)>> AnalyzeQualityStabilityAsync(int materialId, int days = 30);

        #endregion
    }
}
