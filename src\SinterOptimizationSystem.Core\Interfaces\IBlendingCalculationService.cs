using SinterOptimizationSystem.Core.Models;

namespace SinterOptimizationSystem.Core.Interfaces
{
    /// <summary>
    /// 配料计算模式枚举
    /// </summary>
    public enum BlendingMode
    {
        /// <summary>
        /// 人工模式
        /// </summary>
        Manual = 1,
        
        /// <summary>
        /// 自动模式
        /// </summary>
        Automatic = 2
    }

    /// <summary>
    /// 配料计算请求参数
    /// </summary>
    public class BlendingCalculationRequest
    {
        /// <summary>
        /// 计算模式
        /// </summary>
        public BlendingMode Mode { get; set; }

        /// <summary>
        /// 目标产量(吨/小时)
        /// </summary>
        public decimal TargetOutput { get; set; }

        /// <summary>
        /// 目标TFe含量(%)
        /// </summary>
        public decimal TargetTFe { get; set; }

        /// <summary>
        /// 目标碱度(R)
        /// </summary>
        public decimal TargetBasicity { get; set; }

        /// <summary>
        /// 目标MgO含量(%)
        /// </summary>
        public decimal TargetMgO { get; set; }

        /// <summary>
        /// 目标Al2O3含量(%)
        /// </summary>
        public decimal TargetAl2O3 { get; set; }

        /// <summary>
        /// 目标含碳量(%)
        /// </summary>
        public decimal TargetCarbon { get; set; }

        /// <summary>
        /// 成本限制(元/吨)
        /// </summary>
        public decimal? CostLimit { get; set; }

        /// <summary>
        /// 可用原料列表
        /// </summary>
        public List<int> AvailableMaterialIds { get; set; } = new();

        /// <summary>
        /// 固定配比原料(人工模式)
        /// </summary>
        public Dictionary<int, decimal>? FixedRatios { get; set; }

        /// <summary>
        /// 调整仓原料ID
        /// </summary>
        public List<int>? AdjustmentMaterialIds { get; set; }

        /// <summary>
        /// 优化权重
        /// </summary>
        public Dictionary<string, decimal>? OptimizationWeights { get; set; }
    }

    /// <summary>
    /// 配料计算结果
    /// </summary>
    public class BlendingCalculationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 配料方案
        /// </summary>
        public BlendingScheme? BlendingScheme { get; set; }

        /// <summary>
        /// 配料明细
        /// </summary>
        public List<BlendingDetail> BlendingDetails { get; set; } = new();

        /// <summary>
        /// 预测结果
        /// </summary>
        public List<PredictionResult> PredictionResults { get; set; } = new();

        /// <summary>
        /// 优化迭代次数
        /// </summary>
        public int IterationCount { get; set; }

        /// <summary>
        /// 目标函数值
        /// </summary>
        public decimal ObjectiveValue { get; set; }

        /// <summary>
        /// 计算耗时(毫秒)
        /// </summary>
        public long CalculationTime { get; set; }

        /// <summary>
        /// 约束满足情况
        /// </summary>
        public Dictionary<string, bool> ConstraintsSatisfied { get; set; } = new();

        /// <summary>
        /// 警告信息
        /// </summary>
        public List<string> Warnings { get; set; } = new();
    }

    /// <summary>
    /// 配料计算服务接口
    /// </summary>
    public interface IBlendingCalculationService
    {
        #region 配料计算核心功能

        /// <summary>
        /// 执行配料计算
        /// </summary>
        /// <param name="request">计算请求参数</param>
        /// <returns>计算结果</returns>
        Task<BlendingCalculationResult> CalculateBlendingAsync(BlendingCalculationRequest request);

        /// <summary>
        /// 重新计算配料方案
        /// </summary>
        /// <param name="schemeId">方案ID</param>
        /// <param name="updatedParameters">更新的参数</param>
        /// <returns>计算结果</returns>
        Task<BlendingCalculationResult> RecalculateBlendingAsync(int schemeId, BlendingCalculationRequest updatedParameters);

        /// <summary>
        /// 验证配料方案
        /// </summary>
        /// <param name="blendingDetails">配料明细</param>
        /// <returns>验证结果</returns>
        Task<(bool IsValid, string[] Errors)> ValidateBlendingSchemeAsync(IEnumerable<BlendingDetail> blendingDetails);

        #endregion

        #region 下料量计算

        /// <summary>
        /// 计算下料量
        /// </summary>
        /// <param name="blendingDetails">配料明细</param>
        /// <param name="targetOutput">目标产量</param>
        /// <returns>下料量分配结果</returns>
        Task<Dictionary<int, decimal>> CalculateFeedingRatesAsync(IEnumerable<BlendingDetail> blendingDetails, decimal targetOutput);

        /// <summary>
        /// 动态调整下料量
        /// </summary>
        /// <param name="currentRates">当前下料量</param>
        /// <param name="targetAdjustments">目标调整量</param>
        /// <returns>调整后的下料量</returns>
        Task<Dictionary<int, decimal>> AdjustFeedingRatesAsync(
            Dictionary<int, decimal> currentRates, 
            Dictionary<int, decimal> targetAdjustments);

        /// <summary>
        /// 分配故障仓下料量
        /// </summary>
        /// <param name="faultBinId">故障仓ID</param>
        /// <param name="availableBins">可用仓列表</param>
        /// <returns>重新分配结果</returns>
        Task<Dictionary<int, decimal>> RedistributeFeedingRatesAsync(int faultBinId, List<int> availableBins);

        #endregion

        #region 实时调整

        /// <summary>
        /// 根据实时成分调整配比
        /// </summary>
        /// <param name="schemeId">方案ID</param>
        /// <param name="actualCompositions">实际成分</param>
        /// <returns>调整建议</returns>
        Task<BlendingCalculationResult> AdjustByRealTimeCompositionAsync(
            int schemeId, 
            Dictionary<CompositionType, decimal> actualCompositions);

        /// <summary>
        /// 根据库存变化调整配比
        /// </summary>
        /// <param name="schemeId">方案ID</param>
        /// <param name="stockChanges">库存变化</param>
        /// <returns>调整建议</returns>
        Task<BlendingCalculationResult> AdjustByStockChangesAsync(
            int schemeId, 
            Dictionary<int, decimal> stockChanges);

        /// <summary>
        /// 根据工艺参数调整配比
        /// </summary>
        /// <param name="schemeId">方案ID</param>
        /// <param name="processParameters">工艺参数</param>
        /// <returns>调整建议</returns>
        Task<BlendingCalculationResult> AdjustByProcessParametersAsync(
            int schemeId, 
            Dictionary<string, decimal> processParameters);

        #endregion

        #region 异常处理

        /// <summary>
        /// 检测配料异常
        /// </summary>
        /// <param name="schemeId">方案ID</param>
        /// <returns>异常信息</returns>
        Task<List<string>> DetectBlendingAnomaliesAsync(int schemeId);

        /// <summary>
        /// 处理下料异常
        /// </summary>
        /// <param name="binId">料仓ID</param>
        /// <param name="anomalyType">异常类型</param>
        /// <returns>处理建议</returns>
        Task<string> HandleFeedingAnomalyAsync(int binId, string anomalyType);

        /// <summary>
        /// 生成报警信息
        /// </summary>
        /// <param name="schemeId">方案ID</param>
        /// <param name="alarmType">报警类型</param>
        /// <param name="details">详细信息</param>
        /// <returns>报警结果</returns>
        Task<bool> GenerateAlarmAsync(int schemeId, string alarmType, string details);

        #endregion

        #region 优化算法集成

        /// <summary>
        /// 调用Python优化算法
        /// </summary>
        /// <param name="request">计算请求</param>
        /// <returns>优化结果</returns>
        Task<BlendingCalculationResult> CallPythonOptimizationAsync(BlendingCalculationRequest request);

        /// <summary>
        /// 设置优化算法参数
        /// </summary>
        /// <param name="algorithmType">算法类型</param>
        /// <param name="parameters">参数</param>
        /// <returns>设置结果</returns>
        Task<bool> SetOptimizationParametersAsync(string algorithmType, Dictionary<string, object> parameters);

        #endregion
    }
}
