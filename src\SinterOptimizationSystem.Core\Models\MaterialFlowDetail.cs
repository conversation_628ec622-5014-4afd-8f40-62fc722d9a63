using System.ComponentModel.DataAnnotations;

namespace SinterOptimizationSystem.Core.Models
{
    /// <summary>
    /// 料流操作类型枚举
    /// </summary>
    public enum FlowOperationType
    {
        /// <summary>
        /// 入仓
        /// </summary>
        Incoming = 1,
        
        /// <summary>
        /// 下料
        /// </summary>
        Feeding = 2,
        
        /// <summary>
        /// 暂停
        /// </summary>
        Pause = 3,
        
        /// <summary>
        /// 恢复
        /// </summary>
        Resume = 4,
        
        /// <summary>
        /// 调整
        /// </summary>
        Adjustment = 5,
        
        /// <summary>
        /// 异常
        /// </summary>
        Abnormal = 6,
        
        /// <summary>
        /// 维护
        /// </summary>
        Maintenance = 7
    }

    /// <summary>
    /// 料流跟踪详情模型
    /// </summary>
    public class MaterialFlowDetail : BaseEntity
    {
        /// <summary>
        /// 料流ID
        /// </summary>
        public int MaterialFlowId { get; set; }

        /// <summary>
        /// 料流对象
        /// </summary>
        public virtual MaterialFlow MaterialFlow { get; set; } = null!;

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 操作类型
        /// </summary>
        public FlowOperationType OperationType { get; set; }

        /// <summary>
        /// 操作前重量(吨)
        /// </summary>
        public decimal WeightBefore { get; set; }

        /// <summary>
        /// 操作后重量(吨)
        /// </summary>
        public decimal WeightAfter { get; set; }

        /// <summary>
        /// 变化量(吨)
        /// </summary>
        public decimal WeightChange { get; set; }

        /// <summary>
        /// 下料速率(吨/小时)
        /// </summary>
        public decimal? FeedingRate { get; set; }

        /// <summary>
        /// 仓位高度(米)
        /// </summary>
        public decimal? BinLevel { get; set; }

        /// <summary>
        /// 下料口编号
        /// </summary>
        [MaxLength(20)]
        public string? FeedingPortNumber { get; set; }

        /// <summary>
        /// 操作人员
        /// </summary>
        [MaxLength(50)]
        public string? Operator { get; set; }

        /// <summary>
        /// 操作原因
        /// </summary>
        [MaxLength(200)]
        public string? OperationReason { get; set; }

        /// <summary>
        /// 设备状态
        /// </summary>
        [MaxLength(50)]
        public string? EquipmentStatus { get; set; }

        /// <summary>
        /// 工艺参数(JSON格式)
        /// </summary>
        public string? ProcessParameters { get; set; }

        /// <summary>
        /// 质量参数(JSON格式)
        /// </summary>
        public string? QualityParameters { get; set; }

        /// <summary>
        /// 环境参数(JSON格式)
        /// </summary>
        public string? EnvironmentParameters { get; set; }

        /// <summary>
        /// 是否自动操作
        /// </summary>
        public bool IsAutomatic { get; set; } = false;

        /// <summary>
        /// 报警信息
        /// </summary>
        [MaxLength(500)]
        public string? AlarmInfo { get; set; }

        /// <summary>
        /// 处理结果
        /// </summary>
        [MaxLength(500)]
        public string? HandlingResult { get; set; }
    }
}
