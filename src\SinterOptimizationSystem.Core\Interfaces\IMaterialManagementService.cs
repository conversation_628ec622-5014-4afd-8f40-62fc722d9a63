using SinterOptimizationSystem.Core.Models;

namespace SinterOptimizationSystem.Core.Interfaces
{
    /// <summary>
    /// 原料管理服务接口
    /// </summary>
    public interface IMaterialManagementService
    {
        #region 原料基础管理
        
        /// <summary>
        /// 获取所有原料
        /// </summary>
        /// <returns>原料列表</returns>
        Task<IEnumerable<Material>> GetAllMaterialsAsync();

        /// <summary>
        /// 根据ID获取原料
        /// </summary>
        /// <param name="id">原料ID</param>
        /// <returns>原料信息</returns>
        Task<Material?> GetMaterialByIdAsync(int id);

        /// <summary>
        /// 根据编码获取原料
        /// </summary>
        /// <param name="code">原料编码</param>
        /// <returns>原料信息</returns>
        Task<Material?> GetMaterialByCodeAsync(string code);

        /// <summary>
        /// 根据类型获取原料
        /// </summary>
        /// <param name="type">原料类型</param>
        /// <returns>原料列表</returns>
        Task<IEnumerable<Material>> GetMaterialsByTypeAsync(MaterialType type);

        /// <summary>
        /// 添加原料
        /// </summary>
        /// <param name="material">原料信息</param>
        /// <returns>添加结果</returns>
        Task<bool> AddMaterialAsync(Material material);

        /// <summary>
        /// 更新原料
        /// </summary>
        /// <param name="material">原料信息</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateMaterialAsync(Material material);

        /// <summary>
        /// 删除原料
        /// </summary>
        /// <param name="id">原料ID</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteMaterialAsync(int id);

        #endregion

        #region 化学成分管理

        /// <summary>
        /// 获取原料的当前化学成分
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <returns>化学成分列表</returns>
        Task<IEnumerable<MaterialComposition>> GetCurrentCompositionsAsync(int materialId);

        /// <summary>
        /// 获取原料的历史化学成分
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>化学成分历史记录</returns>
        Task<IEnumerable<MaterialComposition>> GetCompositionHistoryAsync(int materialId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 添加化学成分检测结果
        /// </summary>
        /// <param name="compositions">化学成分列表</param>
        /// <returns>添加结果</returns>
        Task<bool> AddCompositionsAsync(IEnumerable<MaterialComposition> compositions);

        /// <summary>
        /// 更新化学成分
        /// </summary>
        /// <param name="composition">化学成分</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateCompositionAsync(MaterialComposition composition);

        /// <summary>
        /// 批量更新原料的当前成分
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <param name="compositions">新的化学成分</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateCurrentCompositionsAsync(int materialId, IEnumerable<MaterialComposition> compositions);

        #endregion

        #region 物理性能管理

        /// <summary>
        /// 获取原料的当前物理性能
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <returns>物理性能列表</returns>
        Task<IEnumerable<MaterialPhysicalProperty>> GetCurrentPhysicalPropertiesAsync(int materialId);

        /// <summary>
        /// 添加物理性能检测结果
        /// </summary>
        /// <param name="properties">物理性能列表</param>
        /// <returns>添加结果</returns>
        Task<bool> AddPhysicalPropertiesAsync(IEnumerable<MaterialPhysicalProperty> properties);

        /// <summary>
        /// 更新物理性能
        /// </summary>
        /// <param name="property">物理性能</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdatePhysicalPropertyAsync(MaterialPhysicalProperty property);

        #endregion

        #region 性价比分析

        /// <summary>
        /// 计算原料性价比
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <returns>性价比分析结果</returns>
        Task<decimal> CalculateCostPerformanceRatioAsync(int materialId);

        /// <summary>
        /// 获取性价比排名
        /// </summary>
        /// <param name="materialType">原料类型</param>
        /// <returns>性价比排名列表</returns>
        Task<IEnumerable<(Material Material, decimal Ratio)>> GetCostPerformanceRankingAsync(MaterialType? materialType = null);

        /// <summary>
        /// 分析原料采购建议
        /// </summary>
        /// <param name="targetComposition">目标成分</param>
        /// <param name="budgetLimit">预算限制</param>
        /// <returns>采购建议</returns>
        Task<IEnumerable<Material>> GetPurchaseRecommendationsAsync(
            Dictionary<CompositionType, decimal> targetComposition, 
            decimal budgetLimit);

        #endregion

        #region 库存管理

        /// <summary>
        /// 更新库存
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <param name="quantity">数量变化</param>
        /// <param name="operationType">操作类型(入库/出库)</param>
        /// <returns>更新结果</returns>
        Task<bool> UpdateStockAsync(int materialId, decimal quantity, string operationType);

        /// <summary>
        /// 获取库存预警列表
        /// </summary>
        /// <returns>库存不足的原料列表</returns>
        Task<IEnumerable<Material>> GetLowStockMaterialsAsync();

        /// <summary>
        /// 预测库存消耗
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <param name="consumptionRate">消耗速率</param>
        /// <returns>预计可用天数</returns>
        Task<int> PredictStockConsumptionAsync(int materialId, decimal consumptionRate);

        #endregion

        #region 数据验证与分析

        /// <summary>
        /// 验证化学成分数据
        /// </summary>
        /// <param name="compositions">化学成分列表</param>
        /// <returns>验证结果</returns>
        Task<(bool IsValid, string[] Errors)> ValidateCompositionsAsync(IEnumerable<MaterialComposition> compositions);

        /// <summary>
        /// 分析成分变化趋势
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <param name="compositionType">成分类型</param>
        /// <param name="days">分析天数</param>
        /// <returns>趋势分析结果</returns>
        Task<(decimal Trend, decimal Variance)> AnalyzeCompositionTrendAsync(
            int materialId, 
            CompositionType compositionType, 
            int days = 30);

        /// <summary>
        /// 检测异常成分
        /// </summary>
        /// <param name="materialId">原料ID</param>
        /// <returns>异常成分列表</returns>
        Task<IEnumerable<MaterialComposition>> DetectAbnormalCompositionsAsync(int materialId);

        #endregion
    }
}
