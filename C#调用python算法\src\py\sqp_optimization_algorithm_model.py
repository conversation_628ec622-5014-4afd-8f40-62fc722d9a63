# ======================== 0. 导入必要的库 ========================
"""
烧结配料优化算法主程序
作者: LZK    
版本: 2.0
更新日期: 2025-07-10

本程序实现了基于序列二次规划(SQP)的烧结配料优化算法。
主要功能:
1. 实现了考虑多个约束条件的配料优化
2. 支持人工初选物料功能
3. 支持配比阈值过滤

"""

import numpy as np          # 用于数值计算
from scipy.optimize import minimize  # 提供优化算法
import pandas as pd         # 用于数据处理
import os                  # 用于文件和路径操作
import datetime           # 用于生成时间戳
import matplotlib.pyplot as plt  # 用于数据可视化
import matplotlib.cm as cm      # 用于颜色映射
from matplotlib.colors import LinearSegmentedColormap  # 用于自定义颜色
import sys                 # 用于系统相关操作
import pickle             # 用于模型序列化
import json               # 用于JSON格式处理

# ======================== 1. 全局配置 ========================
# ======================== 1.1 Matplotlib全局配置 ========================
# 设置matplotlib的默认字体、图片大小、DPI等参数
plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
plt.rcParams['axes.unicode_minus'] = False    # 正确显示负号
plt.rcParams['figure.figsize'] = [12, 8]     # 设置默认图片大小
plt.rcParams['figure.dpi'] = 300             # 设置默认DPI
plt.rcParams['savefig.dpi'] = 300            # 设置保存图片的DPI
plt.rcParams['axes.grid'] = True             # 默认显示网格
plt.rcParams['grid.alpha'] = 0.3             # 设置网格透明度
plt.rcParams['axes.labelsize'] = 12          # 设置轴标签字体大小
plt.rcParams['axes.titlesize'] = 14          # 设置标题字体大小

# ======================== 1.2 自定义颜色方案 ========================
# 定义自定义颜色列表用于可视化图表的颜色设置
COLORS = ['#2ecc71', '#e74c3c', '#3498db', '#f1c40f', '#9b59b6', 
          '#1abc9c', '#e67e22', '#34495e', '#7f8c8d', '#16a085',
          '#d35400', '#8e44ad', '#2980b9', '#c0392b', '#27ae60']

# ======================== 1.3 创建颜色调色板 ========================
# 根据输入数量生成对应数量的颜色列表
def create_color_palette(n):
    """
    创建指定数量的颜色
    Args:
        n: 需要的颜色数量
    Returns:
        list: 颜色列表
    """
    if n <= len(COLORS):
        return COLORS[:n]
    else:
        # 如果需要更多颜色，则循环使用已有颜色
        return COLORS * (n // len(COLORS)) + COLORS[:n % len(COLORS)]

# 优化过程历史记录
objective_history = []

# ======================== 2. 基础数据定义 ========================
# ======================== 2.1 物料名称列表 ========================
# 所有可选用的原料名称
material_names = [
    "碱性精粉", "酸性精粉", "海瑞", "印粉海娜", "巴西粗粉",
    "俄罗斯精粉", "高炉返矿", "回收料", "钢渣", "氧化铁皮",
    "生石灰", "轻烧白云石", "焦粉", "澳粉纵横"
]

# ======================== 2.2 人工初选物料配置 ========================
# 人工设定哪些物料参与计算(True)或不参与计算(False)
manual_selection = [
    False,   # 碱性精粉（不使用）
    False,   # 酸性精粉（不使用）
    False,   # 海瑞（不使用）
    True,    # 印粉海娜
    False,   # 巴西粗粉（不使用）
    True,    # 俄罗斯精粉
    True,    # 高炉返矿
    True,    # 回收料
    True,    # 钢渣
    False,   # 氧化铁皮（不使用）
    True,    # 生石灰
    True,    # 轻烧白云石
    True,    # 焦粉
    True     # 澳粉纵横
]

# ======================== 2.3 湿配比最小阈值设置 ========================
# 设置湿配比的最小阈值，低于此值的配比将被强制设为0
MIN_WET_RATIO_THRESHOLD = 2.0  # 低于此值的配比将被强制为0

# ======================== 3. 原料数据初始化 ========================
# ======================== 3.1 原料化学成分数据矩阵 ========================
# 定义每种原料的化学成分数据矩阵，包括TFe, CaO, SiO2, MgO, Al2O3, H2O, Ig, Price等信息
materials = np.array([
    # TFe,   CaO,   SiO2,  MgO,  Al2O3, H2O,   Ig,    Price
    [63.76, 1.94,  4.95,  1.85, 0.60,  8.20,  1.23,  752.21], # 碱性精粉
    [64.89, 0.70,  6.32,  0.92, 0.72,  9.90, -0.05,  752.21], # 酸性精粉
    [58.07, 0.10,  6.21,  0.28, 2.52,  6.00,  9.07,  822.98], # 海瑞
    [63.66, 0.10,  4.01,  0.24, 2.42,  6.70,  1.60,  832.98], # 印粉海娜
    [64.64, 0.20,  4.69,  0.11, 0.73,  6.70,  1.33, 1473.05], # 巴西粗粉
    [62.95, 1.71,  4.61,  3.70, 2.29, 10.00, -0.35,  772.21], # 俄罗斯精粉
    [55.54, 10.60, 5.59,  2.34, 2.09,  0.50,  1.73,  550.00], # 高炉返矿
    [56.16, 6.56,  6.31,  2.39, 2.51, 10.73,  1.74,  100.00], # 回收料
    [26.46, 28.15, 15.43, 2.79, 2.53,  7.60, 12.05,  550.00], # 钢渣
    [69.73, 0.50,  1.50,  0.00, 2.88,  5.90, -1.52,  750.00], # 氧化铁皮
    [0.00,  71.74, 3.52,  2.28, 1.19,  7.00, 16.33,  219.00], # 生石灰
    [0.00,  42.67, 5.31, 26.12, 0.10,  1.50, 19.73,  183.76], # 轻烧白云石
    [0.19,  0.37,  8.82,  0.22, 3.31, 13.15, 79.40,  520.00], # 焦粉
    [60.80, 0.10,  4.35,  0.20, 2.30,  8.30,  6.89,  832.98]  # 澳粉纵横
])

# ======================== 4. 目标与约束定义 ========================
# ======================== 4.1 目标值设定 ========================
# 设定TFe, 碱度(R), MgO, Al2O3的目标值
targets = {
    'TFe': 55.0,    # 总铁含量目标值
    'R': 1.90,      # 碱度目标值
    'MgO': 2.39,    # 氧化镁含量目标值
    'Al2O3': 1.89   # 氧化铝含量目标值
}

# ======================== 4.2 约束范围设定 ========================
# 设定各指标(TFe, R, MgO, Al2O3, Cost)的上下限范围
ranges = {
    'TFe': [53.5, 56.5],    # TFe含量范围
    'R': [1.75, 2.05],      # 碱度范围
    'MgO': [1.8, 3.0],      # MgO含量范围
    'Al2O3': [1.5, 2.5],    # Al2O3含量范围
    'Cost': [600, 665]      # 成本范围(元/吨)
}

# ======================== 4.3 优化目标权重设置 ========================
# 设定各指标在优化目标中的权重比例
weights = {
    'TFe': 0.5,     # TFe指标权重
    'R': 0.3,       # 碱度指标权重
    'MgO': 0.1,     # MgO指标权重
    'Al2O3': 0.1    # Al2O3指标权重
}

# ======================== 5. 核心计算函数 ========================
# ======================== 5.1 计算烧结矿性质 ========================
# 根据给定配比向量计算烧结矿的性质，包括TFe, R, MgO, Al2O3和成本
def _calculate_sinter_properties(x):
    """
    计算给定配比下的烧结矿性质
    
    Args:
        x: 配比向量 (湿基%)
        
    Returns:
        tuple: (TFe, R, MgO, Al2O3, Cost) 如果计算成功
        None: 如果计算失败
    """
    # 将百分比转换为小数
    wet_ratios = np.array(x) / 100.0
    
    # 提取各项化学成分
    chem_props = materials[:, :5]  # TFe, CaO, SiO2, MgO, Al2O3
    h2o = materials[:, 5] / 100.0  # 水分含量
    ig = materials[:, 6] / 100.0   # 烧损
    prices = materials[:, 7]        # 价格
    
    # 计算干基质量
    dry_mass = wet_ratios * (1 - h2o)
    
    # 计算烧结后质量
    burnt_mass = dry_mass * (1 - ig)
    total_burnt_mass = np.sum(burnt_mass)
    
    # 检查总质量是否有效
    if total_burnt_mass < 1e-6:
        return None, None, None, None, None
    
    # 计算各化学成分质量
    component_mass = dry_mass[:, np.newaxis] * (chem_props / 100.0)
    total_component_mass = np.sum(component_mass, axis=0)
    
    # 计算最终化学成分百分比
    sinter_comp = (total_component_mass / total_burnt_mass) * 100.0
    sinter_TFe, sinter_CaO, sinter_SiO2, sinter_MgO, sinter_Al2O3 = sinter_comp
    
    # 计算碱度
    sinter_R = sinter_CaO / sinter_SiO2 if sinter_SiO2 > 1e-6 else 999
    
    # 计算成本
    total_cost = np.sum(dry_mass * prices) / total_burnt_mass if total_burnt_mass > 0 else 0
    
    return sinter_TFe, sinter_R, sinter_MgO, sinter_Al2O3, total_cost

# ======================== 5.2 优化目标函数 ========================
# 定义优化问题的目标函数，基于加权平方误差计算
def objective(x):
    """
    优化目标函数
    
    Args:
        x: 配比向量
        
    Returns:
        float: 目标函数值(越小越好)
    """
    # 计算烧结矿性质
    res = _calculate_sinter_properties(x)
    if res[0] is None:
        return 1e12  # 返回一个很大的数作为惩罚
    
    sinter_TFe, sinter_R, sinter_MgO, sinter_Al2O3, _ = res
    
    # 计算加权平方误差
    loss = (
        weights['TFe'] * (sinter_TFe - targets['TFe'])**2 +
        weights['R'] * (sinter_R - targets['R'])**2 +
        weights['MgO'] * (sinter_MgO - targets['MgO'])**2 +
        weights['Al2O3'] * (sinter_Al2O3 - targets['Al2O3'])**2
    )
    
    return loss

# ======================== 5.3 带历史记录的目标函数 ========================
# 在目标函数中记录每次迭代的目标函数值，用于后续分析
def objective_with_history(x):
    """
    带历史记录的目标函数
    
    Args:
        x: 配比向量
        
    Returns:
        float: 目标函数值
    """
    obj_val = objective(x)
    objective_history.append(obj_val)
    return obj_val

# ======================== 5.4 总和约束函数 ========================
# 确保所有物料配比的总和等于100%
def constraint_sum(x):
    """
    总和约束函数
    
    Args:
        x: 配比向量
        
    Returns:
        float: 约束偏差值
    """
    return np.sum(x) - 100.0

# ======================== 5.5 约束条件工厂函数 ========================
# 动态生成不同指标的上下限约束函数
def constraint_factory(name, index, low_or_high):
    """
    约束条件工厂函数
    
    Args:
        name: 约束名称
        index: 性质索引
        low_or_high: 'low'表示下限约束,'high'表示上限约束
        
    Returns:
        function: 约束函数
    """
    def constraint(x):
        props = _calculate_sinter_properties(x)
        if props[0] is None:
            return -1
        val = props[index]
        bound = ranges[name][0 if low_or_high == 'low' else 1]
        return val - bound if low_or_high == 'low' else bound - val
    return constraint

# 定义约束条件列表
constraints = [
    {'type': 'eq', 'fun': constraint_sum},  # 配比和为100%
    # TFe约束
    {'type': 'ineq', 'fun': constraint_factory('TFe', 0, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('TFe', 0, 'high')},
    # 碱度约束
    {'type': 'ineq', 'fun': constraint_factory('R', 1, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('R', 1, 'high')},
    # MgO约束
    {'type': 'ineq', 'fun': constraint_factory('MgO', 2, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('MgO', 2, 'high')},
    # Al2O3约束
    {'type': 'ineq', 'fun': constraint_factory('Al2O3', 3, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('Al2O3', 3, 'high')},
    # 成本约束
    {'type': 'ineq', 'fun': constraint_factory('Cost', 4, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('Cost', 4, 'high')}
]

def run_optimization():
    """
    运行优化算法
    
    Returns:
        tuple: (最优配比向量, 优化结果对象, 初始配比向量)
    """
    # 设置初始值
    x0 = np.array([0.0, 0.0, 0.0, 21.0, 0.0, 20.0, 25.0, 7.0, 4.0, 0.0, 6.0, 4.0, 4.0, 9.0])
    
    # 设置边界条件
    bounds_list = [(0, 0)]*3 + [(15, 25), (0, 0), (15, 25), (20, 30), 
                                (5, 10), (2, 6), (0, 0), (5, 8), (2, 5), 
                                (3, 5), (8, 15)]
    
    # 根据人工选择调整边界条件
    current_bounds = list(bounds_list)
    current_x0 = x0.copy()
    unselected_indices = [i for i, selected in enumerate(manual_selection) if not selected]
    
    for i in unselected_indices:
        current_bounds[i] = (0, 0)
        current_x0[i] = 0.0
    
    # 归一化初始值
    active_sum = np.sum(current_x0)
    if active_sum > 1e-6:
        current_x0 = current_x0 / active_sum * 100.0
    
    # 清空优化历史
    objective_history.clear()
    best_solution = None
    
    # 迭代优化
    for i in range(10):
        solution = minimize(
            objective_with_history,
            current_x0,
            method='SLSQP',
            bounds=current_bounds,
            constraints=constraints,
            options={'maxiter': 500, 'ftol': 1e-7, 'disp': True}
        )
        
        if solution.success:
            best_solution = solution
            x_opt = best_solution.x
            
            # 检查是否有配比低于阈值
            below_threshold = [j for j, r in enumerate(x_opt) 
                             if 0 < r < MIN_WET_RATIO_THRESHOLD 
                             and current_bounds[j] != (0,0)]
            
            if not below_threshold:
                return x_opt, best_solution, x0
            
            # 将低于阈值的配比设为0
            for idx in below_threshold:
                current_bounds[idx] = (0, 0)
            current_x0 = x_opt
        else:
            if i == 0:
                # 第一次失败时，添加随机扰动
                current_x0 += np.random.uniform(-1, 1, len(current_x0))
                for j, (low, high) in enumerate(current_bounds):
                    current_x0[j] = np.clip(current_x0[j], low, high)
            else:
                break
    
    return (best_solution.x, best_solution, x0) if best_solution else (None, None, x0)

