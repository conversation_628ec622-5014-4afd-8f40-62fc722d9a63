# ======================== 0. 导入必要的库 ========================
"""
烧结配料优化算法主程序
作者: LZK    
版本: 2.0
更新日期: 2025-07-10

本程序实现了基于序列二次规划(SQP)的烧结配料优化算法。
主要功能:
1. 实现了考虑多个约束条件的配料优化
2. 支持人工初选物料功能
3. 支持配比阈值过滤
4. 提供丰富的可视化分析功能
5. 支持结果导出和模型保存
"""

import numpy as np          # 用于数值计算
from scipy.optimize import minimize  # 提供优化算法
import pandas as pd         # 用于数据处理
import os                  # 用于文件和路径操作
import datetime           # 用于生成时间戳
import matplotlib.pyplot as plt  # 用于数据可视化
import matplotlib.cm as cm      # 用于颜色映射
from matplotlib.colors import LinearSegmentedColormap  # 用于自定义颜色
import sys                 # 用于系统相关操作
import pickle             # 用于模型序列化
import json               # 用于JSON格式处理

# ======================== 1. 全局配置 ========================
# ======================== 1.1 Matplotlib全局配置 ========================
# 设置matplotlib的默认字体、图片大小、DPI等参数
plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
plt.rcParams['axes.unicode_minus'] = False    # 正确显示负号
plt.rcParams['figure.figsize'] = [12, 8]     # 设置默认图片大小
plt.rcParams['figure.dpi'] = 300             # 设置默认DPI
plt.rcParams['savefig.dpi'] = 300            # 设置保存图片的DPI
plt.rcParams['axes.grid'] = True             # 默认显示网格
plt.rcParams['grid.alpha'] = 0.3             # 设置网格透明度
plt.rcParams['axes.labelsize'] = 12          # 设置轴标签字体大小
plt.rcParams['axes.titlesize'] = 14          # 设置标题字体大小

# ======================== 1.2 自定义颜色方案 ========================
# 定义自定义颜色列表用于可视化图表的颜色设置
COLORS = ['#2ecc71', '#e74c3c', '#3498db', '#f1c40f', '#9b59b6', 
          '#1abc9c', '#e67e22', '#34495e', '#7f8c8d', '#16a085',
          '#d35400', '#8e44ad', '#2980b9', '#c0392b', '#27ae60']

# ======================== 1.3 创建颜色调色板 ========================
# 根据输入数量生成对应数量的颜色列表
def create_color_palette(n):
    """
    创建指定数量的颜色
    Args:
        n: 需要的颜色数量
    Returns:
        list: 颜色列表
    """
    if n <= len(COLORS):
        return COLORS[:n]
    else:
        # 如果需要更多颜色，则循环使用已有颜色
        return COLORS * (n // len(COLORS)) + COLORS[:n % len(COLORS)]

# 优化过程历史记录
objective_history = []

# ======================== 2. 基础数据定义 ========================
# ======================== 2.1 物料名称列表 ========================
# 所有可选用的原料名称
material_names = [
    "碱性精粉", "酸性精粉", "海瑞", "印粉海娜", "巴西粗粉",
    "俄罗斯精粉", "高炉返矿", "回收料", "钢渣", "氧化铁皮",
    "生石灰", "轻烧白云石", "焦粉", "澳粉纵横"
]

# ======================== 2.2 人工初选物料配置 ========================
# 人工设定哪些物料参与计算(True)或不参与计算(False)
manual_selection = [
    False,   # 碱性精粉（不使用）
    False,   # 酸性精粉（不使用）
    False,   # 海瑞（不使用）
    True,    # 印粉海娜
    False,   # 巴西粗粉（不使用）
    True,    # 俄罗斯精粉
    True,    # 高炉返矿
    True,    # 回收料
    True,    # 钢渣
    False,   # 氧化铁皮（不使用）
    True,    # 生石灰
    True,    # 轻烧白云石
    True,    # 焦粉
    True     # 澳粉纵横
]

# ======================== 2.3 湿配比最小阈值设置 ========================
# 设置湿配比的最小阈值，低于此值的配比将被强制设为0
MIN_WET_RATIO_THRESHOLD = 2.0  # 低于此值的配比将被强制为0

# ======================== 3. 原料数据初始化 ========================
# ======================== 3.1 原料化学成分数据矩阵 ========================
# 定义每种原料的化学成分数据矩阵，包括TFe, CaO, SiO2, MgO, Al2O3, H2O, Ig, Price等信息
materials = np.array([
    # TFe,   CaO,   SiO2,  MgO,  Al2O3, H2O,   Ig,    Price
    [63.76, 1.94,  4.95,  1.85, 0.60,  8.20,  1.23,  752.21], # 碱性精粉
    [64.89, 0.70,  6.32,  0.92, 0.72,  9.90, -0.05,  752.21], # 酸性精粉
    [58.07, 0.10,  6.21,  0.28, 2.52,  6.00,  9.07,  822.98], # 海瑞
    [63.66, 0.10,  4.01,  0.24, 2.42,  6.70,  1.60,  832.98], # 印粉海娜
    [64.64, 0.20,  4.69,  0.11, 0.73,  6.70,  1.33, 1473.05], # 巴西粗粉
    [62.95, 1.71,  4.61,  3.70, 2.29, 10.00, -0.35,  772.21], # 俄罗斯精粉
    [55.54, 10.60, 5.59,  2.34, 2.09,  0.50,  1.73,  550.00], # 高炉返矿
    [56.16, 6.56,  6.31,  2.39, 2.51, 10.73,  1.74,  100.00], # 回收料
    [26.46, 28.15, 15.43, 2.79, 2.53,  7.60, 12.05,  550.00], # 钢渣
    [69.73, 0.50,  1.50,  0.00, 2.88,  5.90, -1.52,  750.00], # 氧化铁皮
    [0.00,  71.74, 3.52,  2.28, 1.19,  7.00, 16.33,  219.00], # 生石灰
    [0.00,  42.67, 5.31, 26.12, 0.10,  1.50, 19.73,  183.76], # 轻烧白云石
    [0.19,  0.37,  8.82,  0.22, 3.31, 13.15, 79.40,  520.00], # 焦粉
    [60.80, 0.10,  4.35,  0.20, 2.30,  8.30,  6.89,  832.98]  # 澳粉纵横
])

# ======================== 4. 目标与约束定义 ========================
# ======================== 4.1 目标值设定 ========================
# 设定TFe, 碱度(R), MgO, Al2O3的目标值
targets = {
    'TFe': 55.0,    # 总铁含量目标值
    'R': 1.90,      # 碱度目标值
    'MgO': 2.39,    # 氧化镁含量目标值
    'Al2O3': 1.89   # 氧化铝含量目标值
}

# ======================== 4.2 约束范围设定 ========================
# 设定各指标(TFe, R, MgO, Al2O3, Cost)的上下限范围
ranges = {
    'TFe': [53.5, 56.5],    # TFe含量范围
    'R': [1.75, 2.05],      # 碱度范围
    'MgO': [1.8, 3.0],      # MgO含量范围
    'Al2O3': [1.5, 2.5],    # Al2O3含量范围
    'Cost': [600, 665]      # 成本范围(元/吨)
}

# ======================== 4.3 优化目标权重设置 ========================
# 设定各指标在优化目标中的权重比例
weights = {
    'TFe': 0.5,     # TFe指标权重
    'R': 0.3,       # 碱度指标权重
    'MgO': 0.1,     # MgO指标权重
    'Al2O3': 0.1    # Al2O3指标权重
}

# ======================== 5. 核心计算函数 ========================
# ======================== 5.1 计算烧结矿性质 ========================
# 根据给定配比向量计算烧结矿的性质，包括TFe, R, MgO, Al2O3和成本
def _calculate_sinter_properties(x):
    """
    计算给定配比下的烧结矿性质
    
    Args:
        x: 配比向量 (湿基%)
        
    Returns:
        tuple: (TFe, R, MgO, Al2O3, Cost) 如果计算成功
        None: 如果计算失败
    """
    # 将百分比转换为小数
    wet_ratios = np.array(x) / 100.0
    
    # 提取各项化学成分
    chem_props = materials[:, :5]  # TFe, CaO, SiO2, MgO, Al2O3
    h2o = materials[:, 5] / 100.0  # 水分含量
    ig = materials[:, 6] / 100.0   # 烧损
    prices = materials[:, 7]        # 价格
    
    # 计算干基质量
    dry_mass = wet_ratios * (1 - h2o)
    
    # 计算烧结后质量
    burnt_mass = dry_mass * (1 - ig)
    total_burnt_mass = np.sum(burnt_mass)
    
    # 检查总质量是否有效
    if total_burnt_mass < 1e-6:
        return None, None, None, None, None
    
    # 计算各化学成分质量
    component_mass = dry_mass[:, np.newaxis] * (chem_props / 100.0)
    total_component_mass = np.sum(component_mass, axis=0)
    
    # 计算最终化学成分百分比
    sinter_comp = (total_component_mass / total_burnt_mass) * 100.0
    sinter_TFe, sinter_CaO, sinter_SiO2, sinter_MgO, sinter_Al2O3 = sinter_comp
    
    # 计算碱度
    sinter_R = sinter_CaO / sinter_SiO2 if sinter_SiO2 > 1e-6 else 999
    
    # 计算成本
    total_cost = np.sum(dry_mass * prices) / total_burnt_mass if total_burnt_mass > 0 else 0
    
    return sinter_TFe, sinter_R, sinter_MgO, sinter_Al2O3, total_cost

# ======================== 5.2 优化目标函数 ========================
# 定义优化问题的目标函数，基于加权平方误差计算
def objective(x):
    """
    优化目标函数
    
    Args:
        x: 配比向量
        
    Returns:
        float: 目标函数值(越小越好)
    """
    # 计算烧结矿性质
    res = _calculate_sinter_properties(x)
    if res[0] is None:
        return 1e12  # 返回一个很大的数作为惩罚
    
    sinter_TFe, sinter_R, sinter_MgO, sinter_Al2O3, _ = res
    
    # 计算加权平方误差
    loss = (
        weights['TFe'] * (sinter_TFe - targets['TFe'])**2 +
        weights['R'] * (sinter_R - targets['R'])**2 +
        weights['MgO'] * (sinter_MgO - targets['MgO'])**2 +
        weights['Al2O3'] * (sinter_Al2O3 - targets['Al2O3'])**2
    )
    
    return loss

# ======================== 5.3 带历史记录的目标函数 ========================
# 在目标函数中记录每次迭代的目标函数值，用于后续分析
def objective_with_history(x):
    """
    带历史记录的目标函数
    
    Args:
        x: 配比向量
        
    Returns:
        float: 目标函数值
    """
    obj_val = objective(x)
    objective_history.append(obj_val)
    return obj_val

# ======================== 5.4 总和约束函数 ========================
# 确保所有物料配比的总和等于100%
def constraint_sum(x):
    """
    总和约束函数
    
    Args:
        x: 配比向量
        
    Returns:
        float: 约束偏差值
    """
    return np.sum(x) - 100.0

# ======================== 5.5 约束条件工厂函数 ========================
# 动态生成不同指标的上下限约束函数
def constraint_factory(name, index, low_or_high):
    """
    约束条件工厂函数
    
    Args:
        name: 约束名称
        index: 性质索引
        low_or_high: 'low'表示下限约束,'high'表示上限约束
        
    Returns:
        function: 约束函数
    """
    def constraint(x):
        props = _calculate_sinter_properties(x)
        if props[0] is None:
            return -1
        val = props[index]
        bound = ranges[name][0 if low_or_high == 'low' else 1]
        return val - bound if low_or_high == 'low' else bound - val
    return constraint

# 定义约束条件列表
constraints = [
    {'type': 'eq', 'fun': constraint_sum},  # 配比和为100%
    # TFe约束
    {'type': 'ineq', 'fun': constraint_factory('TFe', 0, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('TFe', 0, 'high')},
    # 碱度约束
    {'type': 'ineq', 'fun': constraint_factory('R', 1, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('R', 1, 'high')},
    # MgO约束
    {'type': 'ineq', 'fun': constraint_factory('MgO', 2, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('MgO', 2, 'high')},
    # Al2O3约束
    {'type': 'ineq', 'fun': constraint_factory('Al2O3', 3, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('Al2O3', 3, 'high')},
    # 成本约束
    {'type': 'ineq', 'fun': constraint_factory('Cost', 4, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('Cost', 4, 'high')}
]

def run_optimization():
    """
    运行优化算法
    
    Returns:
        tuple: (最优配比向量, 优化结果对象, 初始配比向量)
    """
    # 设置初始值
    x0 = np.array([0.0, 0.0, 0.0, 21.0, 0.0, 20.0, 25.0, 7.0, 4.0, 0.0, 6.0, 4.0, 4.0, 9.0])
    
    # 设置边界条件
    bounds_list = [(0, 0)]*3 + [(15, 25), (0, 0), (15, 25), (20, 30), 
                                (5, 10), (2, 6), (0, 0), (5, 8), (2, 5), 
                                (3, 5), (8, 15)]
    
    # 根据人工选择调整边界条件
    current_bounds = list(bounds_list)
    current_x0 = x0.copy()
    unselected_indices = [i for i, selected in enumerate(manual_selection) if not selected]
    
    for i in unselected_indices:
        current_bounds[i] = (0, 0)
        current_x0[i] = 0.0
    
    # 归一化初始值
    active_sum = np.sum(current_x0)
    if active_sum > 1e-6:
        current_x0 = current_x0 / active_sum * 100.0
    
    # 清空优化历史
    objective_history.clear()
    best_solution = None
    
    # 迭代优化
    for i in range(10):
        solution = minimize(
            objective_with_history,
            current_x0,
            method='SLSQP',
            bounds=current_bounds,
            constraints=constraints,
            options={'maxiter': 500, 'ftol': 1e-7, 'disp': True}
        )
        
        if solution.success:
            best_solution = solution
            x_opt = best_solution.x
            
            # 检查是否有配比低于阈值
            below_threshold = [j for j, r in enumerate(x_opt) 
                             if 0 < r < MIN_WET_RATIO_THRESHOLD 
                             and current_bounds[j] != (0,0)]
            
            if not below_threshold:
                return x_opt, best_solution, x0
            
            # 将低于阈值的配比设为0
            for idx in below_threshold:
                current_bounds[idx] = (0, 0)
            current_x0 = x_opt
        else:
            if i == 0:
                # 第一次失败时，添加随机扰动
                current_x0 += np.random.uniform(-1, 1, len(current_x0))
                for j, (low, high) in enumerate(current_bounds):
                    current_x0[j] = np.clip(current_x0[j], low, high)
            else:
                break
    
    return (best_solution.x, best_solution, x0) if best_solution else (None, None, x0)

# ======================== 6. 模型保存函数 ========================
# ======================== 6.1 保存优化模型和结果 ========================
# 将优化结果保存为pickle和JSON格式，方便后续加载和使用
def save_model(x_opt, final_props, filepath_prefix):
    """
    保存优化模型和结果
    
    Args:
        x_opt: 最优配比向量
        final_props: 最终属性值
        filepath_prefix: 文件路径前缀
    """
    # 创建模型数据字典
    model_data = {
        'optimal_ratios': {
            material_names[i]: float(ratio) 
            for i, ratio in enumerate(x_opt) if ratio > 0.01
        },
        'properties': {
            k: float(v) for k, v in final_props.items()
        },
        'constraints': ranges,
        'targets': targets,
        'timestamp': datetime.datetime.now().isoformat()
    }
    
    # 保存为Python pickle格式
    pickle_path = f"{filepath_prefix}_model.pkl"
    with open(pickle_path, 'wb') as f:
        pickle.dump(model_data, f)
    
    # 保存为JSON格式(供其他语言使用)
    json_path = f"{filepath_prefix}_model.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(model_data, f, ensure_ascii=False, indent=2)
    
    print(f"模型已保存至:\n- {pickle_path}\n- {json_path}")

# ======================== 7. 可视化函数 ========================
# ======================== 7.1 绘制优化过程收敛曲线 ========================
# 展示优化过程中目标函数值的变化趋势
def plot_convergence(history, filepath):
    """
    绘制优化过程收敛曲线
    Args:
        history: 优化过程中的目标函数值列表
        filepath: 图片保存路径
    """
    fig, ax = plt.subplots()
    
    # 绘制原始数据点和连线
    x = np.arange(len(history))
    ax.plot(x, history, 'o-', color=COLORS[0], alpha=0.4, label='优化过程', markersize=4)
    
    # 添加移动平均线
    window = 5
    if len(history) > window:
        moving_avg = np.convolve(history, np.ones(window)/window, mode='valid')
        ax.plot(x[window-1:], moving_avg, '-', color=COLORS[1], 
                linewidth=2, label='移动平均')
    
    # 设置图表属性
    ax.set_title('优化过程收敛曲线', pad=20)
    ax.set_xlabel('迭代步数')
    ax.set_ylabel('目标函数值')
    ax.grid(True, linestyle='--', alpha=0.7)
    ax.legend()
    
    # 标注最小值点
    min_val = min(history)
    min_idx = history.index(min_val)
    ax.annotate(f'最小值: {min_val:.4f}',
                xy=(min_idx, min_val),
                xytext=(10, 30),
                textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.5),
                arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.3'))
    
    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

# ======================== 7.2 绘制柱状图或水平柱状图 ========================
# 用于展示不同物料的配比分布或其他属性对比
def plot_bar_chart(data, title, xlabel, ylabel, filepath, style='bar'):
    """
    绘制柱状图或水平柱状图
    Args:
        data: 要绘制的数据字典
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        filepath: 图片保存路径
        style: 图表样式，'bar'为竖向柱状图，'barh'为横向柱状图
    """
    fig, ax = plt.subplots()
    colors = create_color_palette(len(data))
    
    if style == 'bar':
        # 绘制竖向柱状图
        bars = ax.bar(range(len(data)), data.values(), color=colors)
        ax.set_xticks(range(len(data)))
        ax.set_xticklabels(data.keys(), rotation=45, ha='right')
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.2f}%',
                   ha='center', va='bottom')
    else:
        # 绘制横向柱状图
        bars = ax.barh(range(len(data)), list(data.values()), color=colors)
        ax.set_yticks(range(len(data)))
        ax.set_yticklabels(data.keys())
        
        # 添加数值标签
        for bar in bars:
            width = bar.get_width()
            ax.text(width, bar.get_y() + bar.get_height()/2.,
                   f'{width:.2f}%',
                   ha='left', va='center')
    
    ax.set_title(title, pad=20)
    ax.set_xlabel(xlabel)
    ax.set_ylabel(ylabel)
    
    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

# ======================== 7.3 绘制约束满足情况图 ========================
# 展示最终结果是否满足各项约束条件
def plot_constraints_satisfaction(final_props, filepath):
    """
    绘制约束满足情况图
    Args:
        final_props: 最终属性值字典
        filepath: 图片保存路径
    """
    prop_names = {
        'TFe': 'TFe (%)', 
        'R': '碱度 (R)', 
        'MgO': 'MgO (%)', 
        'Al2O3': 'Al₂O₃ (%)', 
        'Cost': '成本 (元/吨)'
    }
    
    labels = list(prop_names.values())
    values = list(final_props.values())
    low_bounds = [ranges[k][0] for k in prop_names.keys()]
    high_bounds = [ranges[k][1] for k in prop_names.keys()]
    
    fig, ax = plt.subplots()
    y = np.arange(len(labels))
    
    # 绘制约束范围背景
    for i in range(len(labels)):
        ax.fill_between([low_bounds[i], high_bounds[i]], 
                       [y[i]-0.3]*2, [y[i]+0.3]*2,
                       color='lightgreen', alpha=0.3)
    
    # 绘制实际值
    colors = create_color_palette(len(labels))
    bars = ax.barh(y, values, height=0.4, color=colors)
    
    # 添加约束边界线和标签
    for i in range(len(labels)):
        ax.plot([low_bounds[i]]*2, [y[i]-0.3, y[i]+0.3], 'r--', alpha=0.5)
        ax.plot([high_bounds[i]]*2, [y[i]-0.3, y[i]+0.3], 'r--', alpha=0.5)
        ax.text(values[i], y[i], f' {values[i]:.2f}', 
                va='center', ha='left',
                bbox=dict(facecolor='white', edgecolor='none', alpha=0.7))
    
    ax.set_yticks(y)
    ax.set_yticklabels(labels)
    ax.set_xlabel('数值')
    ax.set_title('各项目标约束满足情况', pad=20)
    
    # 添加图例
    ax.plot([], [], 'r--', label='约束边界')
    ax.fill_between([], [], [], color='lightgreen', alpha=0.3, label='约束范围')
    ax.legend(loc='upper right')
    
    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

# ======================== 7.4 绘制人工初选物料分布图 ========================
# 展示哪些物料参与了计算，哪些未参与
def plot_manual_selection(filepath):
    """
    绘制人工初选物料分布图
    Args:
        filepath: 图片保存路径
    """
    selected = {material_names[i]:1 for i, s in enumerate(manual_selection) if s}
    unselected = {material_names[i]:0 for i, s in enumerate(manual_selection) if not s}
    
    fig, ax = plt.subplots()
    
    # 绘制选中和未选中的物料
    y_selected = np.arange(len(selected))
    y_unselected = np.arange(len(selected), len(selected) + len(unselected))
    
    ax.scatter(np.ones(len(selected)), y_selected, 
              c=COLORS[0], s=100, label='参与计算')
    ax.scatter(np.zeros(len(unselected)), y_unselected, 
              c=COLORS[1], s=100, label='不参与计算')
    
    # 添加物料名称标签
    for i, name in enumerate(selected.keys()):
        ax.text(1.1, i, name, va='center')
    for i, name in enumerate(unselected.keys()):
        ax.text(0.1, i + len(selected), name, va='center')
    
    ax.set_title('人工初选物料分布', pad=20)
    ax.set_xticks([0, 1])
    ax.set_xticklabels(['不参与', '参与'])
    ax.set_yticks([])
    ax.legend()
    
    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

# ======================== 7.5 绘制性质雷达图 ========================
# 使用雷达图展示各性质的实际值与目标值对比
def plot_property_radar(final_props, filepath):
    """
    绘制性质雷达图
    Args:
        final_props: 最终属性值字典
        filepath: 图片保存路径
    """
    properties = ['TFe', 'R', 'MgO', 'Al2O3', 'Cost']
    values = [final_props[prop] for prop in properties]
    target_values = [targets.get(prop, ranges[prop][0]) for prop in properties]
    
    angles = np.linspace(0, 2*np.pi, len(properties), endpoint=False)
    values = np.concatenate((values, [values[0]]))
    target_values = np.concatenate((target_values, [target_values[0]]))
    angles = np.concatenate((angles, [angles[0]]))
    
    fig, ax = plt.subplots(subplot_kw=dict(projection='polar'))
    
    ax.plot(angles, values, 'o-', linewidth=2, label='实际值', color=COLORS[0])
    ax.fill(angles, values, alpha=0.25, color=COLORS[0])
    ax.plot(angles, target_values, 'o-', linewidth=2, label='目标值', color=COLORS[1])
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(properties)
    ax.set_title('性质雷达图', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

# ======================== 7.6 绘制成本构成分析图 ========================
# 分析不同物料对总成本的贡献比例
def plot_cost_breakdown(x_opt, filepath):
    """
    绘制成本构成分析图
    Args:
        x_opt: 最优配比方案
        filepath: 图片保存路径
    """
    costs = []
    names = []
    for i, ratio in enumerate(x_opt):
        if ratio > 0.01:
            cost = (ratio/100) * materials[i, -1]
            costs.append(cost)
            names.append(material_names[i])
    
    fig, ax = plt.subplots()
    colors = create_color_palette(len(costs))
    
    # 绘制饼图
    wedges, texts, autotexts = ax.pie(costs, labels=names, colors=colors,
                                     autopct='%1.1f%%', startangle=90)
    
    # 添加详细成本信息的图例
    ax.legend(wedges, [f'{n}: {c:.2f}元/吨' for n, c in zip(names, costs)],
             title="成本构成",
             loc="center left",
             bbox_to_anchor=(1, 0, 0.5, 1))
    
    ax.set_title('各物料成本贡献分析', pad=20)
    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

# ======================== 7.7 绘制化学成分对比图 ========================
# 对比不同物料的化学成分含量
def plot_composition_comparison(x_opt, filepath):
    """
    绘制化学成分对比图
    Args:
        x_opt: 最优配比方案
        filepath: 图片保存路径
    """
    active_materials = [(i, ratio) for i, ratio in enumerate(x_opt) if ratio > 0.01]
    names = [material_names[i] for i, _ in active_materials]
    
    compositions = {
        'TFe': [materials[i, 0] for i, _ in active_materials],
        'CaO': [materials[i, 1] for i, _ in active_materials],
        'SiO2': [materials[i, 2] for i, _ in active_materials],
        'MgO': [materials[i, 3] for i, _ in active_materials],
        'Al2O3': [materials[i, 4] for i, _ in active_materials]
    }
    
    fig, ax = plt.subplots()
    bottom = np.zeros(len(names))
    colors = create_color_palette(len(compositions))
    
    for (comp_name, comp_values), color in zip(compositions.items(), colors):
        ax.bar(names, comp_values, bottom=bottom, label=comp_name, color=color)
        bottom += np.array(comp_values)
    
    ax.set_title('各物料化学成分对比', pad=20)
    ax.set_xlabel('物料名称')
    ax.set_ylabel('含量 (%)')
    plt.xticks(rotation=45, ha='right')
    plt.legend(title='化学成分')
    
    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

    # 新增可视化函数部分
def plot_material_composition_pie(x_opt, filepath):
    """
    绘制物料组成饼图
    Args:
        x_opt: 最优配比向量
        filepath: 图片保存路径
    """
    selected_materials = {material_names[i]: ratio for i, ratio in enumerate(x_opt) if ratio > 0.01}
    labels = list(selected_materials.keys())
    sizes = list(selected_materials.values())
    colors = create_color_palette(len(labels))

    fig, ax = plt.subplots()
    ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax.axis('equal')  # 保证饼图是圆形
    ax.set_title('最优物料组成比例饼图', pad=20)

    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

def plot_cost_distribution(x_opt, filepath):
    """
    绘制成本分布条形图
    Args:
        x_opt: 最优配比向量
        filepath: 图片保存路径
    """
    prices = materials[:, 7]
    costs = [x_opt[i] / 100 * prices[i] for i in range(len(x_opt)) if x_opt[i] > 0.01]
    selected_materials = [material_names[i] for i in range(len(x_opt)) if x_opt[i] > 0.01]

    fig, ax = plt.subplots()
    colors = create_color_palette(len(selected_materials))
    bars = ax.bar(selected_materials, costs, color=colors)

    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width() / 2, height, f'{height:.2f} 元', ha='center', va='bottom')

    ax.set_title('各物料成本分布条形图', pad=20)
    ax.set_xlabel('物料名称')
    ax.set_ylabel('成本 (元)')
    plt.xticks(rotation=45)

    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

def plot_chemical_correlation_matrix(final_props, filepath):
    """
    绘制化学成分相关性矩阵热力图
    Args:
        final_props: 最终属性值字典
        filepath: 图片保存路径
    """
    chem_props = ['TFe', 'R', 'MgO', 'Al2O3']
    data = np.array([final_props[prop] for prop in chem_props]).reshape(-1, 1)
    corr_matrix = np.corrcoef(data, rowvar=False)

    fig, ax = plt.subplots()
    im = ax.imshow(corr_matrix, cmap='coolwarm')

    # 设置坐标轴标签
    ax.set_xticks(np.arange(len(chem_props)))
    ax.set_yticks(np.arange(len(chem_props)))
    ax.set_xticklabels(chem_props)
    ax.set_yticklabels(chem_props)

    # 显示数值
    for i in range(len(chem_props)):
        for j in range(len(chem_props)):
            text = ax.text(j, i, f'{corr_matrix[i, j]:.2f}', ha='center', va='center', color='w')

    ax.set_title('化学成分相关性矩阵热力图', pad=20)
    plt.colorbar(im)

    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

def plot_material_usage_trend(x_opt, initial_x0, filepath):
    """
    绘制物料使用量变化趋势图
    Args:
        x_opt: 最优配比向量
        initial_x0: 初始配比向量
        filepath: 图片保存路径
    """
    selected_indices = [i for i, ratio in enumerate(x_opt) if ratio > 0.01]
    selected_materials = [material_names[i] for i in selected_indices]
    initial_usage = [initial_x0[i] for i in selected_indices]
    final_usage = [x_opt[i] for i in selected_indices]

    fig, ax = plt.subplots()
    x = np.arange(len(selected_materials))
    width = 0.35

    rects1 = ax.bar(x - width/2, initial_usage, width, label='初始使用量', color=COLORS[0])
    rects2 = ax.bar(x + width/2, final_usage, width, label='最终使用量', color=COLORS[1])

    def autolabel(rects):
        for rect in rects:
            height = rect.get_height()
            ax.annotate(f'{height:.2f}',
                        xy=(rect.get_x() + rect.get_width() / 2, height),
                        xytext=(0, 3),  # 3 points vertical offset
                        textcoords='offset points',
                        ha='center', va='bottom')

    autolabel(rects1)
    autolabel(rects2)

    ax.set_ylabel('使用量 (%)')
    ax.set_title('物料使用量变化趋势图', pad=20)
    ax.set_xticks(x)
    ax.set_xticklabels(selected_materials)
    ax.legend()

    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

def plot_cost_vs_quality(final_props, x_opt, filepath):
    """
    绘制成本与质量指标散点图
    Args:
        final_props: 最终属性值字典
        x_opt: 最优配比向量
        filepath: 图片保存路径
    """
    cost = final_props['Cost']
    quality_metrics = ['TFe', 'R', 'MgO', 'Al2O3']
    metric_values = [final_props[metric] for metric in quality_metrics]

    fig, ax = plt.subplots()
    colors = create_color_palette(len(quality_metrics))

    for i, metric in enumerate(quality_metrics):
        ax.scatter(cost, metric_values[i], color=colors[i], label=metric)

    ax.set_xlabel('成本 (元/吨)')
    ax.set_ylabel('质量指标值')
    ax.set_title('成本与质量指标散点图', pad=20)
    ax.legend()

    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

def plot_water_content_distribution(x_opt, filepath):
    """
    绘制物料水分含量分布箱线图
    Args:
        x_opt: 最优配比向量
        filepath: 图片保存路径
    """
    selected_indices = [i for i, ratio in enumerate(x_opt) if ratio > 0.01]
    water_contents = [materials[i][5] for i in selected_indices]
    selected_materials = [material_names[i] for i in selected_indices]

    fig, ax = plt.subplots()
    ax.boxplot(water_contents, labels=selected_materials)
    ax.set_title('物料水分含量分布箱线图', pad=20)
    ax.set_ylabel('水分含量 (%)')

    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()


# ======================== 7.8 绘制优化结果总览图 ========================
# 综合展示优化结果的多个维度，包括配比分布、目标达成、成本构成等
def plot_optimization_summary(x_opt, final_props, filepath):
    """
    绘制优化结果总览图
    Args:
        x_opt: 最优配比方案
        final_props: 最终属性值字典
        filepath: 图片保存路径
    """
    fig = plt.figure(figsize=(15, 10))
    gs = fig.add_gridspec(2, 2, hspace=0.3, wspace=0.3)
    
    # 1. 配比饼图
    ax1 = fig.add_subplot(gs[0, 0])
    ratios = [(material_names[i], ratio) for i, ratio in enumerate(x_opt) if ratio > 0.01]
    colors = create_color_palette(len(ratios))
    wedges, _, _ = ax1.pie([r[1] for r in ratios], labels=[r[0] for r in ratios],
                          colors=colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title('配比分布')
    
    # 2. 目标达成柱状图
    ax2 = fig.add_subplot(gs[0, 1])
    props = ['TFe', 'R', 'MgO', 'Al2O3']
    achieved = [final_props[p] for p in props]
    target = [targets[p] for p in props]
    x = np.arange(len(props))
    width = 0.35
    ax2.bar(x - width/2, achieved, width, label='实际值', color=COLORS[0])
    ax2.bar(x + width/2, target, width, label='目标值', color=COLORS[1])
    ax2.set_xticks(x)
    ax2.set_xticklabels(props)
    ax2.legend()
    ax2.set_title('目标达成情况')
    
    # 3. 成本分析
    ax3 = fig.add_subplot(gs[1, 0])
    costs = [(material_names[i], (ratio/100) * materials[i, -1])
             for i, ratio in enumerate(x_opt) if ratio > 0.01]
    ax3.barh([c[0] for c in costs], [c[1] for c in costs], color=colors)
    ax3.set_title('成本构成')
    
    # 4. 约束满足情况
    ax4 = fig.add_subplot(gs[1, 1])
    for prop in props:
        ax4.plot([ranges[prop][0], ranges[prop][1]], [prop]*2, 'g-', linewidth=2)
        ax4.plot(final_props[prop], prop, 'ro')
    ax4.set_title('约束满足情况')
    
    plt.suptitle('优化结果总览', fontsize=16, y=1.02)
    plt.tight_layout()
    plt.savefig(filepath, bbox_inches='tight')
    plt.close()

# ======================== 8. 结果处理与输出 ========================
# ======================== 8.1 日志与可视化结果 ========================
# 处理优化结果并生成详细的报告和可视化图表
def log_and_visualize_results(x_opt, solution, x0):
    if x_opt is None:
        print("\n优化失败，无法生成报告和图表。")
        return

    # 创建本次运行的输出目录
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_dir = os.path.join('配料计算的动态规划算法', 'output', f'run_{timestamp}')
    os.makedirs(output_dir, exist_ok=True)
    
    # 准备报告内容
    report_content = []
    def log(message):
        print(message)
        report_content.append(message + '\n')
    
    log("======================== 优化结果报告 ========================")
    log(f"报告生成时间: {timestamp}")
    
    log("\n--- 1. 优化诊断信息 ---")
    log(f"优化是否成功: {solution.success}")
    log(f"终止原因: {solution.message}")
    log(f"迭代次数: {solution.nit}")
    log(f"目标函数最终值: {solution.fun:.6f}")
    
    final_props = dict(zip(['TFe', 'R', 'MgO', 'Al2O3', 'Cost'], _calculate_sinter_properties(x_opt)))
    
    log("\n--- 2. 最终配比方案 ---")
    final_ratios = {material_names[i]: r for i, r in enumerate(x_opt) if r > 0.01}
    for name, ratio in final_ratios.items():
        log(f"{name}: {ratio:.2f}%")

    log("\n--- 3. 烧结矿性质与成本 ---")
    for name, prop in final_props.items():
        log(f"{name}: {prop:.2f} (约束范围: {ranges[name]})")

    # 保存文本报告
    report_path = os.path.join(output_dir, 'optimization_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.writelines(report_content)
    log(f"\n详细报告已保存至: {report_path}")

    # --- 生成增强的可视化图表 ---
    # 基础可视化
    plot_convergence(objective_history, os.path.join(output_dir, "1_convergence_curve.png"))
    plot_bar_chart(final_ratios, '最优配料方案构成', '原料名称', '湿配比 (%)', 
                  os.path.join(output_dir, "2_optimal_recipe.png"))
    plot_constraints_satisfaction(final_props, os.path.join(output_dir, "3_constraints.png"))
    plot_manual_selection(os.path.join(output_dir, "4_manual_selection.png"))
    plot_property_radar(final_props, os.path.join(output_dir, "5_property_radar.png"))
    plot_cost_breakdown(x_opt, os.path.join(output_dir, "6_cost_breakdown.png"))
    plot_composition_comparison(x_opt, os.path.join(output_dir, "7_composition_comparison.png"))
    plot_optimization_summary(x_opt, final_props, os.path.join(output_dir, "8_optimization_summary.png"))
    
    # 新增高级可视化图表
    # 物料组成饼图 - 直观展示各物料占比关系
    plot_material_composition_pie(x_opt, os.path.join(output_dir, "9_material_composition_pie.png"))
    
    # 成本分布条形图 - 展示各物料成本贡献
    plot_cost_distribution(x_opt, os.path.join(output_dir, "10_cost_distribution.png"))
    
    # 化学成分相关性矩阵 - 分析化学成分间的相关性
    plot_chemical_correlation_matrix(final_props, os.path.join(output_dir, "11_chemical_correlation_matrix.png"))
    
    # 物料使用量变化趋势图 - 对比优化前后的物料使用变化
    plot_material_usage_trend(x_opt, x0, os.path.join(output_dir, "12_material_usage_trend.png"))
    
    # 成本与质量指标散点图 - 展示成本与各质量指标的关系
    plot_cost_vs_quality(final_props, x_opt, os.path.join(output_dir, "13_cost_vs_quality.png"))
    
    # 物料水分含量分布箱线图 - 分析水分含量分布情况
    plot_water_content_distribution(x_opt, os.path.join(output_dir, "14_water_content_distribution.png"))
    
    log(f"\n所有可视化图表已保存至: {output_dir}")
    log("==============================================================")


if __name__ == "__main__":
    final_x, final_solution, initial_x0 = run_optimization()
    
    if final_x is not None:
        # 创建输出目录
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        output_dir = os.path.join('配料计算的动态规划算法', 'output', f'run_{timestamp}')
        os.makedirs(output_dir, exist_ok=True)
        
        # 计算最终属性
        final_props = dict(zip(
            ['TFe', 'R', 'MgO', 'Al2O3', 'Cost'],
            _calculate_sinter_properties(final_x)
        ))
        
        # 保存模型
        model_path = os.path.join(output_dir, 'optimization')
        save_model(final_x, final_props, model_path)
        
        # 生成可视化报告
        log_and_visualize_results(final_x, final_solution, initial_x0)
    else:
        print("优化失败，请检查约束条件和初始值设置。")