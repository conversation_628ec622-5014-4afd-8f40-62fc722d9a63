@echo off
chcp 65001 >nul
title 智能烧结配料优化系统

echo.
echo ================================================
echo 🔥 智能烧结配料优化系统
echo ================================================
echo.

echo 选择要执行的操作:
echo.
echo 1. 构建项目
echo 2. 启动 Python 算法服务
echo 3. 启动客户端应用
echo 4. 同时启动服务和客户端
echo 5. 清理项目
echo 6. 退出
echo.

set /p choice=请输入选项 (1-6): 

if "%choice%"=="1" goto build
if "%choice%"=="2" goto python
if "%choice%"=="3" goto client
if "%choice%"=="4" goto both
if "%choice%"=="5" goto clean
if "%choice%"=="6" goto exit

echo 无效选项，请重新选择
pause
goto start

:build
echo.
echo 🔨 构建项目...
powershell -ExecutionPolicy Bypass -File "build.ps1" build -Clean
pause
goto start

:python
echo.
echo 🐍 启动 Python 算法服务...
echo 服务将在 http://127.0.0.1:5000 运行
echo 按 Ctrl+C 停止服务
echo.
cd src\py
python optimization_service.py
pause
goto start

:client
echo.
echo 🖥️ 启动客户端应用...
cd src\csharp\SinterOptimization.Client
dotnet run
pause
goto start

:both
echo.
echo 🚀 同时启动服务和客户端...
echo 首先启动 Python 服务，然后启动客户端
echo.
start "Python Service" cmd /k "cd src\py && python optimization_service.py"
timeout /t 3 /nobreak >nul
start "Client App" cmd /k "cd src\csharp\SinterOptimization.Client && dotnet run"
echo 服务和客户端已启动
pause
goto start

:clean
echo.
echo 🧹 清理项目...
powershell -ExecutionPolicy Bypass -File "build.ps1" clean
pause
goto start

:exit
echo.
echo 👋 再见!
exit /b 0
