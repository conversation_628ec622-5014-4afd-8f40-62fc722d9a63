using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Diagnostics;
using System.Text;
using SinterOptimizationSystem.Core.Interfaces;
using SinterOptimizationSystem.Core.Models;

namespace SinterOptimizationSystem.Services
{
    /// <summary>
    /// Python算法集成服务实现
    /// </summary>
    public class PythonAlgorithmService : IPythonAlgorithmService
    {
        private readonly ILogger<PythonAlgorithmService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _pythonPath;
        private readonly string _scriptsPath;
        private readonly Dictionary<string, PythonAlgorithmOutput> _cache;

        public PythonAlgorithmService(ILogger<PythonAlgorithmService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _pythonPath = _configuration["Python:ExecutablePath"] ?? "python";
            _scriptsPath = _configuration["Python:ScriptsPath"] ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "py");
            _cache = new Dictionary<string, PythonAlgorithmOutput>();
        }

        #region 算法调用

        public async Task<PythonAlgorithmOutput> RunSQPOptimizationAsync(PythonAlgorithmInput input)
        {
            try
            {
                _logger.LogInformation("开始执行SQP优化算法");
                
                // 检查缓存
                var inputHash = CalculateInputHash(input);
                var cachedResult = await GetCachedCalculationResultAsync(inputHash);
                if (cachedResult != null)
                {
                    _logger.LogInformation("使用缓存的计算结果");
                    return cachedResult;
                }

                var stopwatch = Stopwatch.StartNew();
                
                // 准备输入数据
                var inputJson = JsonConvert.SerializeObject(input, Formatting.Indented);
                var inputFilePath = Path.Combine(_scriptsPath, $"input_{Guid.NewGuid()}.json");
                var outputFilePath = Path.Combine(_scriptsPath, $"output_{Guid.NewGuid()}.json");

                try
                {
                    // 写入输入文件
                    await File.WriteAllTextAsync(inputFilePath, inputJson, Encoding.UTF8);

                    // 构建Python命令
                    var scriptPath = Path.Combine(_scriptsPath, "sqp_optimization_algorithm_model.py");
                    var arguments = $"\"{scriptPath}\" \"{inputFilePath}\" \"{outputFilePath}\"";

                    // 执行Python脚本
                    var result = await ExecutePythonScriptAsync(arguments);
                    
                    stopwatch.Stop();

                    if (result.IsSuccess && File.Exists(outputFilePath))
                    {
                        // 读取输出结果
                        var outputJson = await File.ReadAllTextAsync(outputFilePath, Encoding.UTF8);
                        var output = JsonConvert.DeserializeObject<PythonAlgorithmOutput>(outputJson);
                        
                        if (output != null)
                        {
                            output.CalculationTime = stopwatch.ElapsedMilliseconds;
                            
                            // 缓存结果
                            await CacheCalculationResultAsync(inputHash, output);
                            
                            _logger.LogInformation("SQP优化算法执行成功，耗时: {Time}ms", stopwatch.ElapsedMilliseconds);
                            return output;
                        }
                    }

                    return new PythonAlgorithmOutput
                    {
                        IsSuccess = false,
                        ErrorMessage = result.ErrorMessage ?? "算法执行失败",
                        CalculationTime = stopwatch.ElapsedMilliseconds
                    };
                }
                finally
                {
                    // 清理临时文件
                    if (File.Exists(inputFilePath)) File.Delete(inputFilePath);
                    if (File.Exists(outputFilePath)) File.Delete(outputFilePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SQP优化算法执行异常");
                return new PythonAlgorithmOutput
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<PythonAlgorithmOutput> RunLinearProgrammingAsync(PythonAlgorithmInput input)
        {
            // 类似SQP的实现，调用不同的Python脚本
            _logger.LogInformation("线性规划算法暂未实现");
            return await Task.FromResult(new PythonAlgorithmOutput
            {
                IsSuccess = false,
                ErrorMessage = "线性规划算法暂未实现"
            });
        }

        public async Task<PythonAlgorithmOutput> RunGeneticAlgorithmAsync(PythonAlgorithmInput input)
        {
            // 类似SQP的实现，调用不同的Python脚本
            _logger.LogInformation("遗传算法暂未实现");
            return await Task.FromResult(new PythonAlgorithmOutput
            {
                IsSuccess = false,
                ErrorMessage = "遗传算法暂未实现"
            });
        }

        public async Task<PythonAlgorithmOutput> RunParticleSwarmOptimizationAsync(PythonAlgorithmInput input)
        {
            // 类似SQP的实现，调用不同的Python脚本
            _logger.LogInformation("粒子群算法暂未实现");
            return await Task.FromResult(new PythonAlgorithmOutput
            {
                IsSuccess = false,
                ErrorMessage = "粒子群算法暂未实现"
            });
        }

        #endregion

        #region 算法管理

        public async Task<(bool IsAvailable, string Version, string[] MissingPackages)> CheckPythonEnvironmentAsync()
        {
            try
            {
                _logger.LogInformation("检查Python环境");

                var result = await ExecutePythonScriptAsync("--version");
                if (!result.IsSuccess)
                {
                    return (false, "", new[] { "Python未安装或路径配置错误" });
                }

                var version = result.Output?.Trim() ?? "";
                
                // 检查必需的包
                var requiredPackages = new[] { "numpy", "scipy", "pandas", "matplotlib", "flask", "flask-cors" };
                var missingPackages = new List<string>();

                foreach (var package in requiredPackages)
                {
                    var checkResult = await ExecutePythonScriptAsync($"-c \"import {package}\"");
                    if (!checkResult.IsSuccess)
                    {
                        missingPackages.Add(package);
                    }
                }

                _logger.LogInformation("Python环境检查完成，版本: {Version}, 缺失包: {MissingPackages}", 
                    version, string.Join(", ", missingPackages));

                return (missingPackages.Count == 0, version, missingPackages.ToArray());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查Python环境异常");
                return (false, "", new[] { ex.Message });
            }
        }

        public async Task<bool> InstallPythonPackagesAsync(string[] packages)
        {
            try
            {
                _logger.LogInformation("安装Python包: {Packages}", string.Join(", ", packages));

                foreach (var package in packages)
                {
                    var result = await ExecutePythonScriptAsync($"-m pip install {package}");
                    if (!result.IsSuccess)
                    {
                        _logger.LogError("安装包失败: {Package}, 错误: {Error}", package, result.ErrorMessage);
                        return false;
                    }
                }

                _logger.LogInformation("Python包安装成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "安装Python包异常");
                return false;
            }
        }

        public async Task<bool> UpdateAlgorithmScriptAsync(string scriptPath, string scriptContent)
        {
            try
            {
                _logger.LogInformation("更新算法脚本: {ScriptPath}", scriptPath);

                var fullPath = Path.Combine(_scriptsPath, scriptPath);
                var directory = Path.GetDirectoryName(fullPath);
                
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                await File.WriteAllTextAsync(fullPath, scriptContent, Encoding.UTF8);

                _logger.LogInformation("算法脚本更新成功: {ScriptPath}", scriptPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新算法脚本异常: {ScriptPath}", scriptPath);
                return false;
            }
        }

        public async Task<Dictionary<string, string>> GetAlgorithmVersionsAsync()
        {
            try
            {
                var versions = new Dictionary<string, string>();
                
                // 读取算法脚本的版本信息
                var scriptFiles = Directory.GetFiles(_scriptsPath, "*.py");
                foreach (var scriptFile in scriptFiles)
                {
                    var content = await File.ReadAllTextAsync(scriptFile);
                    var versionMatch = System.Text.RegularExpressions.Regex.Match(content, @"版本:\s*(\d+\.\d+)");
                    if (versionMatch.Success)
                    {
                        versions[Path.GetFileNameWithoutExtension(scriptFile)] = versionMatch.Groups[1].Value;
                    }
                }

                return versions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取算法版本信息异常");
                return new Dictionary<string, string>();
            }
        }

        #endregion

        #region 性能监控

        public async Task<Dictionary<string, object>> GetAlgorithmPerformanceStatsAsync(string algorithmType, int days = 7)
        {
            // 简化实现，实际应从数据库获取统计信息
            return await Task.FromResult(new Dictionary<string, object>
            {
                ["TotalExecutions"] = 0,
                ["AverageExecutionTime"] = 0,
                ["SuccessRate"] = 0.0,
                ["ErrorCount"] = 0
            });
        }

        public async Task<bool> LogAlgorithmExecutionAsync(string algorithmType, PythonAlgorithmInput input, PythonAlgorithmOutput output, long executionTime)
        {
            try
            {
                _logger.LogInformation("记录算法执行日志: {AlgorithmType}, 耗时: {ExecutionTime}ms, 成功: {IsSuccess}", 
                    algorithmType, executionTime, output.IsSuccess);
                
                // 实际应保存到数据库
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录算法执行日志异常");
                return false;
            }
        }

        #endregion

        #region 数据转换

        public async Task<PythonAlgorithmInput> ConvertToPythonInputAsync(IEnumerable<Material> materials, IBlendingCalculationService.BlendingCalculationRequest request)
        {
            try
            {
                var materialList = materials.ToList();
                var materialNames = materialList.Select(m => m.Name).ToList();
                
                // 构建原料化学成分矩阵
                var materialsMatrix = new decimal[materialList.Count, 8]; // TFe, CaO, SiO2, MgO, Al2O3, H2O, Ig, Price
                var manualSelection = new bool[materialList.Count];
                var initialRatios = new decimal[materialList.Count];
                var boundsMatrix = new decimal[materialList.Count, 2];

                for (int i = 0; i < materialList.Count; i++)
                {
                    var material = materialList[i];
                    
                    // 设置化学成分（这里使用示例数据，实际应从数据库获取）
                    materialsMatrix[i, 7] = material.Price; // Price
                    
                    // 设置人工选择标志
                    manualSelection[i] = request.AvailableMaterialIds.Contains(material.Id);
                    
                    // 设置初始配比
                    initialRatios[i] = request.FixedRatios?.GetValueOrDefault(material.Id, 0) ?? 0;
                    
                    // 设置边界条件
                    boundsMatrix[i, 0] = material.MinRatio; // 最小值
                    boundsMatrix[i, 1] = material.MaxRatio; // 最大值
                }

                var input = new PythonAlgorithmInput
                {
                    MaterialNames = materialNames,
                    MaterialsMatrix = materialsMatrix,
                    ManualSelection = manualSelection,
                    Targets = new Dictionary<string, decimal>
                    {
                        ["TFe"] = request.TargetTFe,
                        ["R"] = request.TargetBasicity,
                        ["MgO"] = request.TargetMgO,
                        ["Al2O3"] = request.TargetAl2O3
                    },
                    Ranges = new Dictionary<string, decimal[]>
                    {
                        ["TFe"] = new[] { request.TargetTFe - 1.5m, request.TargetTFe + 1.5m },
                        ["R"] = new[] { request.TargetBasicity - 0.15m, request.TargetBasicity + 0.15m },
                        ["MgO"] = new[] { request.TargetMgO - 0.6m, request.TargetMgO + 0.6m },
                        ["Al2O3"] = new[] { request.TargetAl2O3 - 0.5m, request.TargetAl2O3 + 0.5m },
                        ["Cost"] = new[] { 600m, request.CostLimit ?? 665m }
                    },
                    Weights = request.OptimizationWeights ?? new Dictionary<string, decimal>
                    {
                        ["TFe"] = 0.5m,
                        ["R"] = 0.3m,
                        ["MgO"] = 0.1m,
                        ["Al2O3"] = 0.1m
                    },
                    InitialRatios = initialRatios,
                    BoundsMatrix = boundsMatrix
                };

                return await Task.FromResult(input);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "转换为Python输入格式异常");
                throw;
            }
        }

        public async Task<IBlendingCalculationService.BlendingCalculationResult> ConvertFromPythonOutputAsync(PythonAlgorithmOutput pythonOutput, IEnumerable<Material> materials)
        {
            try
            {
                var result = new IBlendingCalculationService.BlendingCalculationResult
                {
                    IsSuccess = pythonOutput.IsSuccess,
                    ErrorMessage = pythonOutput.ErrorMessage,
                    IterationCount = pythonOutput.IterationCount,
                    ObjectiveValue = pythonOutput.ObjectiveValue,
                    CalculationTime = pythonOutput.CalculationTime,
                    ConstraintsSatisfied = pythonOutput.ConstraintsSatisfied,
                    Warnings = pythonOutput.Warnings
                };

                if (pythonOutput.IsSuccess && pythonOutput.OptimalRatios.Length > 0)
                {
                    var materialList = materials.ToList();
                    var blendingDetails = new List<BlendingDetail>();

                    for (int i = 0; i < pythonOutput.OptimalRatios.Length && i < materialList.Count; i++)
                    {
                        if (pythonOutput.OptimalRatios[i] > 0.01m) // 只包含有效配比
                        {
                            blendingDetails.Add(new BlendingDetail
                            {
                                MaterialId = materialList[i].Id,
                                Material = materialList[i],
                                WetRatio = pythonOutput.OptimalRatios[i],
                                DryRatio = pythonOutput.OptimalRatios[i] * 0.9m, // 简化计算
                                CostContribution = pythonOutput.OptimalRatios[i] / 100 * materialList[i].Price,
                                IsEnabled = true
                            });
                        }
                    }

                    result.BlendingDetails = blendingDetails;

                    // 创建预测结果
                    var predictionResults = new List<PredictionResult>();
                    foreach (var property in pythonOutput.FinalProperties)
                    {
                        predictionResults.Add(new PredictionResult
                        {
                            PredictionType = PredictionType.ChemicalComposition,
                            Algorithm = PredictionAlgorithm.MaterialBalance,
                            IndicatorName = property.Key,
                            PredictedValue = property.Value,
                            Confidence = 95m,
                            PredictionTime = DateTime.Now
                        });
                    }

                    result.PredictionResults = predictionResults;
                }

                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "转换Python输出格式异常");
                throw;
            }
        }

        #endregion

        #region 缓存管理

        public async Task<bool> CacheCalculationResultAsync(string inputHash, PythonAlgorithmOutput output, int expireMinutes = 60)
        {
            try
            {
                _cache[inputHash] = output;
                
                // 简化实现，实际应使用Redis等缓存系统
                _ = Task.Delay(TimeSpan.FromMinutes(expireMinutes)).ContinueWith(_ => _cache.Remove(inputHash));
                
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "缓存计算结果异常");
                return false;
            }
        }

        public async Task<PythonAlgorithmOutput?> GetCachedCalculationResultAsync(string inputHash)
        {
            try
            {
                return await Task.FromResult(_cache.GetValueOrDefault(inputHash));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取缓存计算结果异常");
                return null;
            }
        }

        public async Task<bool> ClearExpiredCacheAsync()
        {
            try
            {
                // 简化实现
                _cache.Clear();
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期缓存异常");
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 执行Python脚本
        /// </summary>
        private async Task<(bool IsSuccess, string? Output, string? ErrorMessage)> ExecutePythonScriptAsync(string arguments)
        {
            try
            {
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = _pythonPath,
                    Arguments = arguments,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    WorkingDirectory = _scriptsPath
                };

                using var process = new Process { StartInfo = processStartInfo };
                process.Start();

                var outputTask = process.StandardOutput.ReadToEndAsync();
                var errorTask = process.StandardError.ReadToEndAsync();

                await process.WaitForExitAsync();

                var output = await outputTask;
                var error = await errorTask;

                if (process.ExitCode == 0)
                {
                    return (true, output, null);
                }
                else
                {
                    return (false, output, error);
                }
            }
            catch (Exception ex)
            {
                return (false, null, ex.Message);
            }
        }

        /// <summary>
        /// 计算输入参数哈希值
        /// </summary>
        private string CalculateInputHash(PythonAlgorithmInput input)
        {
            var json = JsonConvert.SerializeObject(input);
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(json));
            return Convert.ToBase64String(hash);
        }

        #endregion
    }
}
