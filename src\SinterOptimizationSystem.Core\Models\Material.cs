using System.ComponentModel.DataAnnotations;

namespace SinterOptimizationSystem.Core.Models
{
    /// <summary>
    /// 原料类型枚举
    /// </summary>
    public enum MaterialType
    {
        /// <summary>
        /// 铁矿粉
        /// </summary>
        IronOrePowder = 1,
        
        /// <summary>
        /// 熔剂
        /// </summary>
        Flux = 2,
        
        /// <summary>
        /// 燃料
        /// </summary>
        Fuel = 3,
        
        /// <summary>
        /// 返矿
        /// </summary>
        ReturnOre = 4,
        
        /// <summary>
        /// 回收料
        /// </summary>
        RecycledMaterial = 5,
        
        /// <summary>
        /// 其他
        /// </summary>
        Other = 6
    }

    /// <summary>
    /// 原料信息模型
    /// </summary>
    public class Material : BaseEntity
    {
        /// <summary>
        /// 原料名称
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 原料编码
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 原料类型
        /// </summary>
        public MaterialType Type { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        [MaxLength(200)]
        public string? Supplier { get; set; }

        /// <summary>
        /// 产地
        /// </summary>
        [MaxLength(100)]
        public string? Origin { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 最小配比限制(%)
        /// </summary>
        public decimal MinRatio { get; set; } = 0;

        /// <summary>
        /// 最大配比限制(%)
        /// </summary>
        public decimal MaxRatio { get; set; } = 100;

        /// <summary>
        /// 当前库存量(吨)
        /// </summary>
        public decimal CurrentStock { get; set; } = 0;

        /// <summary>
        /// 安全库存量(吨)
        /// </summary>
        public decimal SafetyStock { get; set; } = 0;

        /// <summary>
        /// 单价(元/吨)
        /// </summary>
        public decimal Price { get; set; } = 0;

        /// <summary>
        /// 化学成分集合
        /// </summary>
        public virtual ICollection<MaterialComposition> Compositions { get; set; } = new List<MaterialComposition>();

        /// <summary>
        /// 物理性能集合
        /// </summary>
        public virtual ICollection<MaterialPhysicalProperty> PhysicalProperties { get; set; } = new List<MaterialPhysicalProperty>();
    }
}
