using System.ComponentModel.DataAnnotations;

namespace SinterOptimizationSystem.Core.Models
{
    /// <summary>
    /// 配料方案状态枚举
    /// </summary>
    public enum BlendingSchemeStatus
    {
        /// <summary>
        /// 草稿
        /// </summary>
        Draft = 1,
        
        /// <summary>
        /// 待审核
        /// </summary>
        PendingReview = 2,
        
        /// <summary>
        /// 已审核
        /// </summary>
        Approved = 3,
        
        /// <summary>
        /// 执行中
        /// </summary>
        InExecution = 4,
        
        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 5,
        
        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 6
    }

    /// <summary>
    /// 配料方案模型
    /// </summary>
    public class BlendingScheme : BaseEntity
    {
        /// <summary>
        /// 方案名称
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 方案编号
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string SchemeNumber { get; set; } = string.Empty;

        /// <summary>
        /// 方案状态
        /// </summary>
        public BlendingSchemeStatus Status { get; set; } = BlendingSchemeStatus.Draft;

        /// <summary>
        /// 目标产量(吨/小时)
        /// </summary>
        public decimal TargetOutput { get; set; }

        /// <summary>
        /// 目标TFe含量(%)
        /// </summary>
        public decimal TargetTFe { get; set; }

        /// <summary>
        /// 目标碱度(R)
        /// </summary>
        public decimal TargetBasicity { get; set; }

        /// <summary>
        /// 目标MgO含量(%)
        /// </summary>
        public decimal TargetMgO { get; set; }

        /// <summary>
        /// 目标Al2O3含量(%)
        /// </summary>
        public decimal TargetAl2O3 { get; set; }

        /// <summary>
        /// 目标含碳量(%)
        /// </summary>
        public decimal TargetCarbon { get; set; }

        /// <summary>
        /// 目标成本(元/吨)
        /// </summary>
        public decimal TargetCost { get; set; }

        /// <summary>
        /// 实际TFe含量(%)
        /// </summary>
        public decimal? ActualTFe { get; set; }

        /// <summary>
        /// 实际碱度(R)
        /// </summary>
        public decimal? ActualBasicity { get; set; }

        /// <summary>
        /// 实际MgO含量(%)
        /// </summary>
        public decimal? ActualMgO { get; set; }

        /// <summary>
        /// 实际Al2O3含量(%)
        /// </summary>
        public decimal? ActualAl2O3 { get; set; }

        /// <summary>
        /// 实际含碳量(%)
        /// </summary>
        public decimal? ActualCarbon { get; set; }

        /// <summary>
        /// 实际成本(元/吨)
        /// </summary>
        public decimal? ActualCost { get; set; }

        /// <summary>
        /// 优化算法类型
        /// </summary>
        [MaxLength(50)]
        public string? OptimizationAlgorithm { get; set; }

        /// <summary>
        /// 优化参数(JSON格式)
        /// </summary>
        public string? OptimizationParameters { get; set; }

        /// <summary>
        /// 开始执行时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束执行时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 审核人
        /// </summary>
        [MaxLength(50)]
        public string? Reviewer { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? ReviewTime { get; set; }

        /// <summary>
        /// 审核意见
        /// </summary>
        [MaxLength(500)]
        public string? ReviewComments { get; set; }

        /// <summary>
        /// 配料明细集合
        /// </summary>
        public virtual ICollection<BlendingDetail> BlendingDetails { get; set; } = new List<BlendingDetail>();

        /// <summary>
        /// 预测结果集合
        /// </summary>
        public virtual ICollection<PredictionResult> PredictionResults { get; set; } = new List<PredictionResult>();
    }
}
