using System.ComponentModel.DataAnnotations;

namespace SinterOptimizationSystem.Core.Models
{
    /// <summary>
    /// 料流状态枚举
    /// </summary>
    public enum MaterialFlowStatus
    {
        /// <summary>
        /// 入仓
        /// </summary>
        Incoming = 1,
        
        /// <summary>
        /// 储存中
        /// </summary>
        Storing = 2,
        
        /// <summary>
        /// 下料中
        /// </summary>
        Feeding = 3,
        
        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 4,
        
        /// <summary>
        /// 异常
        /// </summary>
        Abnormal = 5
    }

    /// <summary>
    /// 料流跟踪模型
    /// </summary>
    public class MaterialFlow : BaseEntity
    {
        /// <summary>
        /// 批次号
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string BatchNumber { get; set; } = string.Empty;

        /// <summary>
        /// 原料ID
        /// </summary>
        public int MaterialId { get; set; }

        /// <summary>
        /// 原料对象
        /// </summary>
        public virtual Material Material { get; set; } = null!;

        /// <summary>
        /// 料仓编号
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string BinNumber { get; set; } = string.Empty;

        /// <summary>
        /// 批重(吨)
        /// </summary>
        public decimal BatchWeight { get; set; }

        /// <summary>
        /// 剩余重量(吨)
        /// </summary>
        public decimal RemainingWeight { get; set; }

        /// <summary>
        /// 入仓时间
        /// </summary>
        public DateTime IncomingTime { get; set; }

        /// <summary>
        /// 开始下料时间
        /// </summary>
        public DateTime? StartFeedingTime { get; set; }

        /// <summary>
        /// 结束下料时间
        /// </summary>
        public DateTime? EndFeedingTime { get; set; }

        /// <summary>
        /// 料流状态
        /// </summary>
        public MaterialFlowStatus Status { get; set; } = MaterialFlowStatus.Incoming;

        /// <summary>
        /// 仓位高度(米)
        /// </summary>
        public decimal? BinLevel { get; set; }

        /// <summary>
        /// 下料速率(吨/小时)
        /// </summary>
        public decimal? FeedingRate { get; set; }

        /// <summary>
        /// 累计下料量(吨)
        /// </summary>
        public decimal CumulativeFeedingAmount { get; set; } = 0;

        /// <summary>
        /// 预计完成时间
        /// </summary>
        public DateTime? EstimatedCompletionTime { get; set; }

        /// <summary>
        /// 质量等级
        /// </summary>
        [MaxLength(20)]
        public string? QualityGrade { get; set; }

        /// <summary>
        /// 供应商批次号
        /// </summary>
        [MaxLength(50)]
        public string? SupplierBatchNumber { get; set; }

        /// <summary>
        /// 检验报告编号
        /// </summary>
        [MaxLength(50)]
        public string? InspectionReportNumber { get; set; }

        /// <summary>
        /// 是否混料
        /// </summary>
        public bool IsMixed { get; set; } = false;

        /// <summary>
        /// 混料比例(%)
        /// </summary>
        public decimal? MixingRatio { get; set; }

        /// <summary>
        /// 温度(℃)
        /// </summary>
        public decimal? Temperature { get; set; }

        /// <summary>
        /// 湿度(%)
        /// </summary>
        public decimal? Humidity { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        [MaxLength(500)]
        public string? AbnormalInfo { get; set; }

        /// <summary>
        /// 处理措施
        /// </summary>
        [MaxLength(500)]
        public string? HandlingMeasures { get; set; }

        /// <summary>
        /// 料流跟踪详情集合
        /// </summary>
        public virtual ICollection<MaterialFlowDetail> FlowDetails { get; set; } = new List<MaterialFlowDetail>();
    }
}
