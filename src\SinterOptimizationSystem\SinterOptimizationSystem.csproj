<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyTitle>智能优化配料系统</AssemblyTitle>
    <AssemblyDescription>企业级烧结配料智能优化系统客户端</AssemblyDescription>
    <AssemblyCompany>钢铁企业</AssemblyCompany>
    <AssemblyProduct>智能优化配料系统</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.5" />
    <PackageReference Include="DevExpress.Win.Grid" Version="23.2.3" />
    <PackageReference Include="DevExpress.Win.Charts" Version="23.2.3" />
    <PackageReference Include="DevExpress.Win.Navigation" Version="23.2.3" />
    <PackageReference Include="OxyPlot.WindowsForms" Version="2.1.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SinterOptimizationSystem.Core\SinterOptimizationSystem.Core.csproj" />
    <ProjectReference Include="..\SinterOptimizationSystem.Services\SinterOptimizationSystem.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
