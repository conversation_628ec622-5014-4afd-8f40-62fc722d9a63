using Microsoft.Extensions.Logging;
using SinterOptimizationSystem.Core.Interfaces;
using SinterOptimizationSystem.Core.Models;
using System.Diagnostics;

namespace SinterOptimizationSystem.Services
{
    /// <summary>
    /// 配料计算服务实现
    /// </summary>
    public class BlendingCalculationService : IBlendingCalculationService
    {
        private readonly ILogger<BlendingCalculationService> _logger;
        private readonly IMaterialManagementService _materialService;
        private readonly IPythonAlgorithmService _pythonAlgorithmService;
        private readonly List<BlendingScheme> _blendingSchemes; // 模拟数据存储

        public BlendingCalculationService(
            ILogger<BlendingCalculationService> logger,
            IMaterialManagementService materialService,
            IPythonAlgorithmService pythonAlgorithmService)
        {
            _logger = logger;
            _materialService = materialService;
            _pythonAlgorithmService = pythonAlgorithmService;
            _blendingSchemes = new List<BlendingScheme>();
        }

        #region 配料计算核心功能

        public async Task<BlendingCalculationResult> CalculateBlendingAsync(BlendingCalculationRequest request)
        {
            try
            {
                _logger.LogInformation("开始执行配料计算，模式: {Mode}", request.Mode);
                var stopwatch = Stopwatch.StartNew();

                // 验证输入参数
                var validationResult = await ValidateCalculationRequestAsync(request);
                if (!validationResult.IsValid)
                {
                    return new BlendingCalculationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = string.Join("; ", validationResult.Errors)
                    };
                }

                // 获取可用原料
                var availableMaterials = await GetAvailableMaterialsAsync(request.AvailableMaterialIds);
                if (!availableMaterials.Any())
                {
                    return new BlendingCalculationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "没有可用的原料"
                    };
                }

                BlendingCalculationResult result;

                if (request.Mode == BlendingMode.Manual)
                {
                    // 人工模式：使用固定配比进行计算
                    result = await CalculateManualBlendingAsync(request, availableMaterials);
                }
                else
                {
                    // 自动模式：调用Python优化算法
                    result = await CallPythonOptimizationAsync(request);
                }

                stopwatch.Stop();
                result.CalculationTime = stopwatch.ElapsedMilliseconds;

                if (result.IsSuccess)
                {
                    // 保存配料方案
                    await SaveBlendingSchemeAsync(result, request);
                    
                    _logger.LogInformation("配料计算完成，耗时: {Time}ms", stopwatch.ElapsedMilliseconds);
                }
                else
                {
                    _logger.LogWarning("配料计算失败: {Error}", result.ErrorMessage);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "配料计算异常");
                return new BlendingCalculationResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<BlendingCalculationResult> RecalculateBlendingAsync(int schemeId, BlendingCalculationRequest updatedParameters)
        {
            try
            {
                _logger.LogInformation("重新计算配料方案: {SchemeId}", schemeId);

                var existingScheme = _blendingSchemes.FirstOrDefault(s => s.Id == schemeId);
                if (existingScheme == null)
                {
                    return new BlendingCalculationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "配料方案不存在"
                    };
                }

                // 使用更新的参数重新计算
                var result = await CalculateBlendingAsync(updatedParameters);
                
                if (result.IsSuccess && result.BlendingScheme != null)
                {
                    // 更新现有方案
                    existingScheme.TargetTFe = updatedParameters.TargetTFe;
                    existingScheme.TargetBasicity = updatedParameters.TargetBasicity;
                    existingScheme.TargetMgO = updatedParameters.TargetMgO;
                    existingScheme.TargetAl2O3 = updatedParameters.TargetAl2O3;
                    existingScheme.TargetCarbon = updatedParameters.TargetCarbon;
                    existingScheme.UpdatedAt = DateTime.Now;
                    
                    result.BlendingScheme = existingScheme;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新计算配料方案异常: {SchemeId}", schemeId);
                return new BlendingCalculationResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<(bool IsValid, string[] Errors)> ValidateBlendingSchemeAsync(IEnumerable<BlendingDetail> blendingDetails)
        {
            try
            {
                var errors = new List<string>();
                var details = blendingDetails.ToList();

                // 检查配比总和
                var totalRatio = details.Sum(d => d.WetRatio);
                if (Math.Abs(totalRatio - 100) > 0.1m)
                {
                    errors.Add($"配比总和不等于100%，当前为: {totalRatio:F2}%");
                }

                // 检查单个配比范围
                foreach (var detail in details)
                {
                    if (detail.WetRatio < detail.MinRatio || detail.WetRatio > detail.MaxRatio)
                    {
                        errors.Add($"原料 {detail.Material?.Name} 配比超出范围 [{detail.MinRatio}-{detail.MaxRatio}]%");
                    }

                    if (detail.CurrentStock <= 0)
                    {
                        errors.Add($"原料 {detail.Material?.Name} 库存不足");
                    }
                }

                // 检查关键原料
                var ironOreRatio = details.Where(d => d.Material?.Type == MaterialType.IronOrePowder).Sum(d => d.WetRatio);
                if (ironOreRatio < 60)
                {
                    errors.Add("铁矿粉配比过低，可能影响烧结矿质量");
                }

                return await Task.FromResult((errors.Count == 0, errors.ToArray()));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证配料方案异常");
                return (false, new[] { ex.Message });
            }
        }

        #endregion

        #region 下料量计算

        public async Task<Dictionary<int, decimal>> CalculateFeedingRatesAsync(IEnumerable<BlendingDetail> blendingDetails, decimal targetOutput)
        {
            try
            {
                _logger.LogInformation("计算下料量，目标产量: {TargetOutput} 吨/小时", targetOutput);

                var feedingRates = new Dictionary<int, decimal>();
                
                foreach (var detail in blendingDetails)
                {
                    if (detail.IsEnabled && detail.WetRatio > 0)
                    {
                        // 计算下料量 = 目标产量 × 配比 / 100
                        var feedingRate = targetOutput * detail.WetRatio / 100;
                        feedingRates[detail.MaterialId] = feedingRate;
                        
                        // 更新配料明细中的下料量
                        detail.FeedingRate = feedingRate;
                    }
                }

                _logger.LogInformation("下料量计算完成，涉及原料数量: {Count}", feedingRates.Count);
                return await Task.FromResult(feedingRates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算下料量异常");
                throw;
            }
        }

        public async Task<Dictionary<int, decimal>> AdjustFeedingRatesAsync(Dictionary<int, decimal> currentRates, Dictionary<int, decimal> targetAdjustments)
        {
            try
            {
                _logger.LogInformation("动态调整下料量");

                var adjustedRates = new Dictionary<int, decimal>(currentRates);

                foreach (var adjustment in targetAdjustments)
                {
                    if (adjustedRates.ContainsKey(adjustment.Key))
                    {
                        var newRate = adjustedRates[adjustment.Key] + adjustment.Value;
                        adjustedRates[adjustment.Key] = Math.Max(0, newRate); // 确保不为负数
                    }
                }

                _logger.LogInformation("下料量调整完成");
                return await Task.FromResult(adjustedRates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "动态调整下料量异常");
                throw;
            }
        }

        public async Task<Dictionary<int, decimal>> RedistributeFeedingRatesAsync(int faultBinId, List<int> availableBins)
        {
            try
            {
                _logger.LogInformation("重新分配故障仓下料量，故障仓: {FaultBinId}", faultBinId);

                var redistributedRates = new Dictionary<int, decimal>();

                // 简化实现：将故障仓的下料量平均分配给可用仓
                // 实际应根据原料类型、库存等因素进行智能分配
                if (availableBins.Count > 0)
                {
                    var faultBinRate = 10m; // 假设故障仓的下料量
                    var redistributedRate = faultBinRate / availableBins.Count;

                    foreach (var binId in availableBins)
                    {
                        redistributedRates[binId] = redistributedRate;
                    }
                }

                _logger.LogInformation("故障仓下料量重新分配完成");
                return await Task.FromResult(redistributedRates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新分配故障仓下料量异常");
                throw;
            }
        }

        #endregion

        #region 实时调整

        public async Task<BlendingCalculationResult> AdjustByRealTimeCompositionAsync(int schemeId, Dictionary<CompositionType, decimal> actualCompositions)
        {
            try
            {
                _logger.LogInformation("根据实时成分调整配比，方案ID: {SchemeId}", schemeId);

                var scheme = _blendingSchemes.FirstOrDefault(s => s.Id == schemeId);
                if (scheme == null)
                {
                    return new BlendingCalculationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "配料方案不存在"
                    };
                }

                // 分析成分偏差
                var deviations = new Dictionary<string, decimal>();
                
                if (actualCompositions.ContainsKey(CompositionType.TFe))
                {
                    deviations["TFe"] = actualCompositions[CompositionType.TFe] - scheme.TargetTFe;
                }

                // 根据偏差调整配比（简化实现）
                var adjustmentSuggestions = new List<string>();
                
                foreach (var deviation in deviations)
                {
                    if (Math.Abs(deviation.Value) > 0.5m)
                    {
                        adjustmentSuggestions.Add($"{deviation.Key} 偏差 {deviation.Value:F2}%，建议调整相关原料配比");
                    }
                }

                return new BlendingCalculationResult
                {
                    IsSuccess = true,
                    BlendingScheme = scheme,
                    Warnings = adjustmentSuggestions
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据实时成分调整配比异常");
                return new BlendingCalculationResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<BlendingCalculationResult> AdjustByStockChangesAsync(int schemeId, Dictionary<int, decimal> stockChanges)
        {
            try
            {
                _logger.LogInformation("根据库存变化调整配比，方案ID: {SchemeId}", schemeId);

                var scheme = _blendingSchemes.FirstOrDefault(s => s.Id == schemeId);
                if (scheme == null)
                {
                    return new BlendingCalculationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "配料方案不存在"
                    };
                }

                var warnings = new List<string>();

                // 检查库存变化对配料方案的影响
                foreach (var stockChange in stockChanges)
                {
                    var material = await _materialService.GetMaterialByIdAsync(stockChange.Key);
                    if (material != null && stockChange.Value < 0)
                    {
                        warnings.Add($"原料 {material.Name} 库存减少 {Math.Abs(stockChange.Value)} 吨");
                        
                        if (material.CurrentStock + stockChange.Value <= material.SafetyStock)
                        {
                            warnings.Add($"原料 {material.Name} 库存即将不足，建议调整配比或补充库存");
                        }
                    }
                }

                return new BlendingCalculationResult
                {
                    IsSuccess = true,
                    BlendingScheme = scheme,
                    Warnings = warnings
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据库存变化调整配比异常");
                return new BlendingCalculationResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<BlendingCalculationResult> AdjustByProcessParametersAsync(int schemeId, Dictionary<string, decimal> processParameters)
        {
            try
            {
                _logger.LogInformation("根据工艺参数调整配比，方案ID: {SchemeId}", schemeId);

                var scheme = _blendingSchemes.FirstOrDefault(s => s.Id == schemeId);
                if (scheme == null)
                {
                    return new BlendingCalculationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "配料方案不存在"
                    };
                }

                var warnings = new List<string>();

                // 分析工艺参数变化
                foreach (var parameter in processParameters)
                {
                    switch (parameter.Key.ToLower())
                    {
                        case "temperature":
                            if (parameter.Value > 1300)
                                warnings.Add("烧结温度过高，建议增加熔剂配比");
                            break;
                        case "airflow":
                            if (parameter.Value < 1.5m)
                                warnings.Add("风量偏低，建议调整燃料配比");
                            break;
                        case "moisture":
                            if (parameter.Value > 8)
                                warnings.Add("水分过高，建议调整配比或延长混合时间");
                            break;
                    }
                }

                return new BlendingCalculationResult
                {
                    IsSuccess = true,
                    BlendingScheme = scheme,
                    Warnings = warnings
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据工艺参数调整配比异常");
                return new BlendingCalculationResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        #endregion

        #region 异常处理

        public async Task<List<string>> DetectBlendingAnomaliesAsync(int schemeId)
        {
            try
            {
                _logger.LogInformation("检测配料异常，方案ID: {SchemeId}", schemeId);

                var anomalies = new List<string>();
                var scheme = _blendingSchemes.FirstOrDefault(s => s.Id == schemeId);
                
                if (scheme == null)
                {
                    anomalies.Add("配料方案不存在");
                    return anomalies;
                }

                // 检测各种异常情况
                if (scheme.ActualTFe.HasValue && Math.Abs(scheme.ActualTFe.Value - scheme.TargetTFe) > 1.0m)
                {
                    anomalies.Add($"TFe含量偏差过大: 目标{scheme.TargetTFe}%, 实际{scheme.ActualTFe}%");
                }

                if (scheme.ActualBasicity.HasValue && Math.Abs(scheme.ActualBasicity.Value - scheme.TargetBasicity) > 0.1m)
                {
                    anomalies.Add($"碱度偏差过大: 目标{scheme.TargetBasicity}, 实际{scheme.ActualBasicity}");
                }

                if (scheme.ActualCost.HasValue && scheme.ActualCost > scheme.TargetCost * 1.1m)
                {
                    anomalies.Add($"成本超出预算: 目标{scheme.TargetCost}元/吨, 实际{scheme.ActualCost}元/吨");
                }

                return await Task.FromResult(anomalies);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检测配料异常失败");
                return new List<string> { ex.Message };
            }
        }

        public async Task<string> HandleFeedingAnomalyAsync(int binId, string anomalyType)
        {
            try
            {
                _logger.LogInformation("处理下料异常，料仓ID: {BinId}, 异常类型: {AnomalyType}", binId, anomalyType);

                var suggestion = anomalyType.ToLower() switch
                {
                    "blockage" => "料仓堵塞，建议停止下料并进行疏通",
                    "empty" => "料仓空仓，建议切换到备用料仓",
                    "overflow" => "下料量过大，建议降低下料速率",
                    "underflow" => "下料量不足，建议检查下料设备",
                    _ => "未知异常，建议人工检查"
                };

                return await Task.FromResult(suggestion);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理下料异常失败");
                return ex.Message;
            }
        }

        public async Task<bool> GenerateAlarmAsync(int schemeId, string alarmType, string details)
        {
            try
            {
                _logger.LogWarning("生成报警，方案ID: {SchemeId}, 类型: {AlarmType}, 详情: {Details}", 
                    schemeId, alarmType, details);

                // 实际应保存到数据库并发送通知
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成报警失败");
                return false;
            }
        }

        #endregion

        #region 优化算法集成

        public async Task<BlendingCalculationResult> CallPythonOptimizationAsync(BlendingCalculationRequest request)
        {
            try
            {
                _logger.LogInformation("调用Python优化算法");

                // 获取可用原料
                var availableMaterials = await GetAvailableMaterialsAsync(request.AvailableMaterialIds);
                
                // 转换为Python输入格式
                var pythonInput = await _pythonAlgorithmService.ConvertToPythonInputAsync(availableMaterials, request);
                
                // 执行SQP优化算法
                var pythonOutput = await _pythonAlgorithmService.RunSQPOptimizationAsync(pythonInput);
                
                // 转换为C#结果格式
                var result = await _pythonAlgorithmService.ConvertFromPythonOutputAsync(pythonOutput, availableMaterials);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调用Python优化算法异常");
                return new BlendingCalculationResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<bool> SetOptimizationParametersAsync(string algorithmType, Dictionary<string, object> parameters)
        {
            try
            {
                _logger.LogInformation("设置优化算法参数，算法类型: {AlgorithmType}", algorithmType);

                // 实际应保存参数配置
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置优化算法参数异常");
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 验证计算请求参数
        /// </summary>
        private async Task<(bool IsValid, string[] Errors)> ValidateCalculationRequestAsync(BlendingCalculationRequest request)
        {
            var errors = new List<string>();

            if (request.TargetOutput <= 0)
                errors.Add("目标产量必须大于0");

            if (request.TargetTFe <= 0 || request.TargetTFe > 100)
                errors.Add("目标TFe含量必须在0-100%之间");

            if (request.TargetBasicity <= 0)
                errors.Add("目标碱度必须大于0");

            if (!request.AvailableMaterialIds.Any())
                errors.Add("必须选择至少一种可用原料");

            return await Task.FromResult((errors.Count == 0, errors.ToArray()));
        }

        /// <summary>
        /// 获取可用原料
        /// </summary>
        private async Task<IEnumerable<Material>> GetAvailableMaterialsAsync(List<int> materialIds)
        {
            var materials = new List<Material>();
            
            foreach (var id in materialIds)
            {
                var material = await _materialService.GetMaterialByIdAsync(id);
                if (material != null && material.IsActive && material.CurrentStock > 0)
                {
                    materials.Add(material);
                }
            }

            return materials;
        }

        /// <summary>
        /// 人工模式配料计算
        /// </summary>
        private async Task<BlendingCalculationResult> CalculateManualBlendingAsync(BlendingCalculationRequest request, IEnumerable<Material> availableMaterials)
        {
            try
            {
                _logger.LogInformation("执行人工模式配料计算");

                var blendingDetails = new List<BlendingDetail>();
                var totalRatio = 0m;

                foreach (var material in availableMaterials)
                {
                    if (request.FixedRatios?.ContainsKey(material.Id) == true)
                    {
                        var ratio = request.FixedRatios[material.Id];
                        if (ratio > 0)
                        {
                            blendingDetails.Add(new BlendingDetail
                            {
                                MaterialId = material.Id,
                                Material = material,
                                WetRatio = ratio,
                                DryRatio = ratio * 0.9m, // 简化计算
                                CostContribution = ratio / 100 * material.Price,
                                IsEnabled = true
                            });
                            totalRatio += ratio;
                        }
                    }
                }

                // 验证配比总和
                if (Math.Abs(totalRatio - 100) > 0.1m)
                {
                    return new BlendingCalculationResult
                    {
                        IsSuccess = false,
                        ErrorMessage = $"配比总和不等于100%，当前为: {totalRatio:F2}%"
                    };
                }

                // 计算下料量
                var feedingRates = await CalculateFeedingRatesAsync(blendingDetails, request.TargetOutput);

                return new BlendingCalculationResult
                {
                    IsSuccess = true,
                    BlendingDetails = blendingDetails,
                    ConstraintsSatisfied = new Dictionary<string, bool> { ["TotalRatio"] = true }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "人工模式配料计算异常");
                return new BlendingCalculationResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 保存配料方案
        /// </summary>
        private async Task<bool> SaveBlendingSchemeAsync(BlendingCalculationResult result, BlendingCalculationRequest request)
        {
            try
            {
                if (result.BlendingScheme == null)
                {
                    result.BlendingScheme = new BlendingScheme
                    {
                        Id = _blendingSchemes.Count + 1,
                        Name = $"配料方案_{DateTime.Now:yyyyMMdd_HHmmss}",
                        SchemeNumber = $"PL{DateTime.Now:yyyyMMddHHmmss}",
                        Status = BlendingSchemeStatus.Draft,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };
                }

                // 设置目标值
                result.BlendingScheme.TargetOutput = request.TargetOutput;
                result.BlendingScheme.TargetTFe = request.TargetTFe;
                result.BlendingScheme.TargetBasicity = request.TargetBasicity;
                result.BlendingScheme.TargetMgO = request.TargetMgO;
                result.BlendingScheme.TargetAl2O3 = request.TargetAl2O3;
                result.BlendingScheme.TargetCarbon = request.TargetCarbon;
                result.BlendingScheme.TargetCost = request.CostLimit ?? 0;

                // 设置配料明细
                foreach (var detail in result.BlendingDetails)
                {
                    detail.BlendingSchemeId = result.BlendingScheme.Id;
                    detail.BlendingScheme = result.BlendingScheme;
                }

                result.BlendingScheme.BlendingDetails = result.BlendingDetails;

                _blendingSchemes.Add(result.BlendingScheme);

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配料方案异常");
                return false;
            }
        }

        #endregion
    }
}
