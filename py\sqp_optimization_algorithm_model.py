#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能优化配料系统 - SQP优化算法模型
版本: 1.0
作者: 系统开发团队
日期: 2024-01-01
"""

import sys
import json
import numpy as np
import time
from scipy.optimize import minimize
from typing import Dict, List, Tuple, Any


class SQPOptimizationModel:
    """SQP优化算法模型"""
    
    def __init__(self):
        self.materials_matrix = None
        self.material_names = []
        self.targets = {}
        self.ranges = {}
        self.weights = {}
        self.bounds = []
        self.manual_selection = []
        self.initial_ratios = []
        self.min_wet_ratio_threshold = 2.0
        self.max_iterations = 500
        self.tolerance = 1e-7
        
    def load_input(self, input_data: Dict[str, Any]) -> bool:
        """加载输入数据"""
        try:
            self.material_names = input_data.get('MaterialNames', [])
            self.materials_matrix = np.array(input_data.get('MaterialsMatrix', []))
            self.manual_selection = np.array(input_data.get('ManualSelection', []))
            self.targets = input_data.get('Targets', {})
            self.ranges = input_data.get('Ranges', {})
            self.weights = input_data.get('Weights', {})
            self.initial_ratios = np.array(input_data.get('InitialRatios', []))
            
            # 构建边界条件
            bounds_matrix = np.array(input_data.get('BoundsMatrix', []))
            if bounds_matrix.size > 0:
                self.bounds = [(bounds_matrix[i, 0], bounds_matrix[i, 1]) 
                              for i in range(bounds_matrix.shape[0])]
            
            self.min_wet_ratio_threshold = input_data.get('MinWetRatioThreshold', 2.0)
            self.max_iterations = input_data.get('MaxIterations', 500)
            self.tolerance = input_data.get('Tolerance', 1e-7)
            
            return True
        except Exception as e:
            print(f"加载输入数据失败: {e}")
            return False
    
    def objective_function(self, x: np.ndarray) -> float:
        """目标函数"""
        try:
            # 计算加权成分
            compositions = self.calculate_compositions(x)
            
            # 计算目标函数值（最小化偏差）
            objective_value = 0.0
            
            # TFe偏差
            if 'TFe' in self.targets and 'TFe' in self.weights:
                tfe_deviation = abs(compositions['TFe'] - self.targets['TFe'])
                objective_value += self.weights['TFe'] * tfe_deviation
            
            # 碱度偏差
            if 'R' in self.targets and 'R' in self.weights:
                basicity = compositions['CaO'] / compositions['SiO2'] if compositions['SiO2'] > 0 else 0
                basicity_deviation = abs(basicity - self.targets['R'])
                objective_value += self.weights['R'] * basicity_deviation
            
            # MgO偏差
            if 'MgO' in self.targets and 'MgO' in self.weights:
                mgo_deviation = abs(compositions['MgO'] - self.targets['MgO'])
                objective_value += self.weights['MgO'] * mgo_deviation
            
            # Al2O3偏差
            if 'Al2O3' in self.targets and 'Al2O3' in self.weights:
                al2o3_deviation = abs(compositions['Al2O3'] - self.targets['Al2O3'])
                objective_value += self.weights['Al2O3'] * al2o3_deviation
            
            return objective_value
        except Exception as e:
            print(f"目标函数计算错误: {e}")
            return 1e6  # 返回大值表示不可行解
    
    def calculate_compositions(self, x: np.ndarray) -> Dict[str, float]:
        """计算加权成分"""
        compositions = {}
        
        if self.materials_matrix.size == 0:
            return compositions
        
        try:
            # 假设材料矩阵列顺序: TFe, CaO, SiO2, MgO, Al2O3, H2O, Ig, Price
            total_weight = np.sum(x)
            if total_weight == 0:
                return compositions
            
            # 计算加权平均成分
            compositions['TFe'] = np.sum(x * self.materials_matrix[:, 0]) / total_weight
            compositions['CaO'] = np.sum(x * self.materials_matrix[:, 1]) / total_weight
            compositions['SiO2'] = np.sum(x * self.materials_matrix[:, 2]) / total_weight
            compositions['MgO'] = np.sum(x * self.materials_matrix[:, 3]) / total_weight
            compositions['Al2O3'] = np.sum(x * self.materials_matrix[:, 4]) / total_weight
            compositions['H2O'] = np.sum(x * self.materials_matrix[:, 5]) / total_weight
            compositions['Ig'] = np.sum(x * self.materials_matrix[:, 6]) / total_weight
            
            # 计算成本
            if self.materials_matrix.shape[1] > 7:
                compositions['Cost'] = np.sum(x * self.materials_matrix[:, 7]) / total_weight
            
        except Exception as e:
            print(f"成分计算错误: {e}")
        
        return compositions
    
    def constraint_total_ratio(self, x: np.ndarray) -> float:
        """配比总和约束"""
        return np.sum(x) - 100.0
    
    def constraint_tfe_range(self, x: np.ndarray) -> Tuple[float, float]:
        """TFe范围约束"""
        compositions = self.calculate_compositions(x)
        tfe = compositions.get('TFe', 0)
        
        if 'TFe' in self.ranges:
            tfe_range = self.ranges['TFe']
            return tfe - tfe_range[0], tfe_range[1] - tfe  # 下界, 上界
        
        return 0.0, 0.0
    
    def constraint_basicity_range(self, x: np.ndarray) -> Tuple[float, float]:
        """碱度范围约束"""
        compositions = self.calculate_compositions(x)
        cao = compositions.get('CaO', 0)
        sio2 = compositions.get('SiO2', 1)  # 避免除零
        basicity = cao / sio2 if sio2 > 0 else 0
        
        if 'R' in self.ranges:
            r_range = self.ranges['R']
            return basicity - r_range[0], r_range[1] - basicity
        
        return 0.0, 0.0
    
    def constraint_mgo_range(self, x: np.ndarray) -> Tuple[float, float]:
        """MgO范围约束"""
        compositions = self.calculate_compositions(x)
        mgo = compositions.get('MgO', 0)
        
        if 'MgO' in self.ranges:
            mgo_range = self.ranges['MgO']
            return mgo - mgo_range[0], mgo_range[1] - mgo
        
        return 0.0, 0.0
    
    def constraint_al2o3_range(self, x: np.ndarray) -> Tuple[float, float]:
        """Al2O3范围约束"""
        compositions = self.calculate_compositions(x)
        al2o3 = compositions.get('Al2O3', 0)
        
        if 'Al2O3' in self.ranges:
            al2o3_range = self.ranges['Al2O3']
            return al2o3 - al2o3_range[0], al2o3_range[1] - al2o3
        
        return 0.0, 0.0
    
    def constraint_cost_range(self, x: np.ndarray) -> float:
        """成本约束"""
        compositions = self.calculate_compositions(x)
        cost = compositions.get('Cost', 0)
        
        if 'Cost' in self.ranges:
            cost_range = self.ranges['Cost']
            return cost_range[1] - cost  # 成本上限约束
        
        return 0.0
    
    def optimize(self) -> Dict[str, Any]:
        """执行优化"""
        start_time = time.time()
        
        try:
            # 检查输入数据
            if self.materials_matrix.size == 0 or len(self.material_names) == 0:
                return {
                    'IsSuccess': False,
                    'ErrorMessage': '输入数据不完整',
                    'CalculationTime': int((time.time() - start_time) * 1000)
                }
            
            # 设置初始值
            n_materials = len(self.material_names)
            if len(self.initial_ratios) == n_materials and np.sum(self.initial_ratios) > 0:
                x0 = self.initial_ratios.copy()
            else:
                # 均匀分配初始值
                x0 = np.ones(n_materials) * (100.0 / n_materials)
            
            # 设置边界条件
            if len(self.bounds) != n_materials:
                self.bounds = [(0.0, 100.0) for _ in range(n_materials)]
            
            # 设置约束条件
            constraints = [
                {'type': 'eq', 'fun': self.constraint_total_ratio}
            ]
            
            # 添加范围约束
            if 'TFe' in self.ranges:
                constraints.extend([
                    {'type': 'ineq', 'fun': lambda x: self.constraint_tfe_range(x)[0]},
                    {'type': 'ineq', 'fun': lambda x: self.constraint_tfe_range(x)[1]}
                ])
            
            if 'R' in self.ranges:
                constraints.extend([
                    {'type': 'ineq', 'fun': lambda x: self.constraint_basicity_range(x)[0]},
                    {'type': 'ineq', 'fun': lambda x: self.constraint_basicity_range(x)[1]}
                ])
            
            if 'MgO' in self.ranges:
                constraints.extend([
                    {'type': 'ineq', 'fun': lambda x: self.constraint_mgo_range(x)[0]},
                    {'type': 'ineq', 'fun': lambda x: self.constraint_mgo_range(x)[1]}
                ])
            
            if 'Al2O3' in self.ranges:
                constraints.extend([
                    {'type': 'ineq', 'fun': lambda x: self.constraint_al2o3_range(x)[0]},
                    {'type': 'ineq', 'fun': lambda x: self.constraint_al2o3_range(x)[1]}
                ])
            
            if 'Cost' in self.ranges:
                constraints.append({
                    'type': 'ineq', 'fun': self.constraint_cost_range
                })
            
            # 执行SQP优化
            result = minimize(
                fun=self.objective_function,
                x0=x0,
                method='SLSQP',
                bounds=self.bounds,
                constraints=constraints,
                options={
                    'maxiter': self.max_iterations,
                    'ftol': self.tolerance,
                    'disp': False
                }
            )
            
            calculation_time = int((time.time() - start_time) * 1000)
            
            if result.success:
                # 计算最终成分
                final_compositions = self.calculate_compositions(result.x)
                
                # 检查约束满足情况
                constraints_satisfied = {}
                constraints_satisfied['TotalRatio'] = abs(np.sum(result.x) - 100.0) < 0.01
                
                if 'TFe' in self.ranges:
                    tfe_bounds = self.constraint_tfe_range(result.x)
                    constraints_satisfied['TFe'] = tfe_bounds[0] >= -0.01 and tfe_bounds[1] >= -0.01
                
                if 'R' in self.ranges:
                    r_bounds = self.constraint_basicity_range(result.x)
                    constraints_satisfied['Basicity'] = r_bounds[0] >= -0.01 and r_bounds[1] >= -0.01
                
                # 生成警告信息
                warnings = []
                for i, ratio in enumerate(result.x):
                    if 0 < ratio < self.min_wet_ratio_threshold:
                        warnings.append(f"原料 {self.material_names[i]} 配比过低: {ratio:.2f}%")
                
                return {
                    'IsSuccess': True,
                    'OptimalRatios': result.x.tolist(),
                    'FinalProperties': final_compositions,
                    'IterationCount': result.nit,
                    'ObjectiveValue': float(result.fun),
                    'ConstraintsSatisfied': constraints_satisfied,
                    'CalculationTime': calculation_time,
                    'TerminationReason': result.message,
                    'Warnings': warnings,
                    'DetailedResults': json.dumps({
                        'optimization_result': {
                            'success': result.success,
                            'status': result.status,
                            'message': result.message,
                            'nfev': result.nfev,
                            'njev': result.njev,
                            'nit': result.nit
                        }
                    }, ensure_ascii=False, indent=2)
                }
            else:
                return {
                    'IsSuccess': False,
                    'ErrorMessage': f"优化失败: {result.message}",
                    'CalculationTime': calculation_time,
                    'TerminationReason': result.message
                }
                
        except Exception as e:
            calculation_time = int((time.time() - start_time) * 1000)
            return {
                'IsSuccess': False,
                'ErrorMessage': f"优化过程异常: {str(e)}",
                'CalculationTime': calculation_time
            }


def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("用法: python sqp_optimization_algorithm_model.py <input_file> <output_file>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        # 读取输入数据
        with open(input_file, 'r', encoding='utf-8') as f:
            input_data = json.load(f)
        
        # 创建优化模型
        model = SQPOptimizationModel()
        
        # 加载输入数据
        if not model.load_input(input_data):
            raise Exception("输入数据加载失败")
        
        # 执行优化
        result = model.optimize()
        
        # 写入输出结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print("优化计算完成")
        
    except Exception as e:
        # 写入错误结果
        error_result = {
            'IsSuccess': False,
            'ErrorMessage': str(e),
            'CalculationTime': 0
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(error_result, f, ensure_ascii=False, indent=2)
        except:
            pass
        
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
