using Microsoft.Extensions.Logging;
using SinterOptimizationSystem.Core.Interfaces;
using SinterOptimizationSystem.Core.Models;

namespace SinterOptimizationSystem
{
    /// <summary>
    /// 原料管理窗体
    /// </summary>
    public partial class MaterialManagementForm : Form
    {
        private readonly ILogger<MaterialManagementForm> _logger;
        private readonly IMaterialManagementService _materialService;

        // 控件
        private DataGridView _materialGridView;
        private Panel _buttonPanel;
        private Button _addButton;
        private Button _editButton;
        private Button _deleteButton;
        private Button _refreshButton;
        private GroupBox _filterGroupBox;
        private ComboBox _typeFilterComboBox;
        private TextBox _nameFilterTextBox;
        private Button _searchButton;

        public MaterialManagementForm(ILogger<MaterialManagementForm> logger, IMaterialManagementService materialService)
        {
            _logger = logger;
            _materialService = materialService;
            InitializeComponent();
            LoadDataAsync();
        }

        private void InitializeComponent()
        {
            // 设置窗体属性
            Text = "原料管理";
            Size = new Size(1200, 800);
            StartPosition = FormStartPosition.CenterParent;

            // 创建过滤区域
            CreateFilterArea();

            // 创建数据网格
            CreateDataGrid();

            // 创建按钮面板
            CreateButtonPanel();
        }

        private void CreateFilterArea()
        {
            _filterGroupBox = new GroupBox
            {
                Text = "查询条件",
                Height = 80,
                Dock = DockStyle.Top,
                Padding = new Padding(10)
            };

            var typeLabel = new Label
            {
                Text = "原料类型:",
                Location = new Point(10, 25),
                Size = new Size(70, 23)
            };

            _typeFilterComboBox = new ComboBox
            {
                Location = new Point(85, 22),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _typeFilterComboBox.Items.Add("全部");
            _typeFilterComboBox.Items.AddRange(Enum.GetNames(typeof(MaterialType)));
            _typeFilterComboBox.SelectedIndex = 0;

            var nameLabel = new Label
            {
                Text = "原料名称:",
                Location = new Point(220, 25),
                Size = new Size(70, 23)
            };

            _nameFilterTextBox = new TextBox
            {
                Location = new Point(295, 22),
                Size = new Size(150, 23)
            };

            _searchButton = new Button
            {
                Text = "查询",
                Location = new Point(460, 20),
                Size = new Size(75, 27)
            };
            _searchButton.Click += SearchButton_Click;

            _filterGroupBox.Controls.AddRange(new Control[]
            {
                typeLabel, _typeFilterComboBox, nameLabel, _nameFilterTextBox, _searchButton
            });

            Controls.Add(_filterGroupBox);
        }

        private void CreateDataGrid()
        {
            _materialGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false
            };

            // 添加列
            _materialGridView.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "ID", Width = 60, DataPropertyName = "Id" },
                new DataGridViewTextBoxColumn { Name = "Code", HeaderText = "编码", Width = 100, DataPropertyName = "Code" },
                new DataGridViewTextBoxColumn { Name = "Name", HeaderText = "名称", Width = 150, DataPropertyName = "Name" },
                new DataGridViewTextBoxColumn { Name = "Type", HeaderText = "类型", Width = 100, DataPropertyName = "Type" },
                new DataGridViewTextBoxColumn { Name = "Supplier", HeaderText = "供应商", Width = 120, DataPropertyName = "Supplier" },
                new DataGridViewTextBoxColumn { Name = "Origin", HeaderText = "产地", Width = 100, DataPropertyName = "Origin" },
                new DataGridViewTextBoxColumn { Name = "Price", HeaderText = "价格(元/吨)", Width = 100, DataPropertyName = "Price" },
                new DataGridViewTextBoxColumn { Name = "CurrentStock", HeaderText = "当前库存(吨)", Width = 120, DataPropertyName = "CurrentStock" },
                new DataGridViewTextBoxColumn { Name = "SafetyStock", HeaderText = "安全库存(吨)", Width = 120, DataPropertyName = "SafetyStock" },
                new DataGridViewCheckBoxColumn { Name = "IsActive", HeaderText = "启用", Width = 60, DataPropertyName = "IsActive" },
                new DataGridViewTextBoxColumn { Name = "CreatedAt", HeaderText = "创建时间", Width = 140, DataPropertyName = "CreatedAt" }
            });

            // 设置数字列的格式
            _materialGridView.Columns["Price"].DefaultCellStyle.Format = "F2";
            _materialGridView.Columns["CurrentStock"].DefaultCellStyle.Format = "F2";
            _materialGridView.Columns["SafetyStock"].DefaultCellStyle.Format = "F2";
            _materialGridView.Columns["CreatedAt"].DefaultCellStyle.Format = "yyyy-MM-dd HH:mm";

            // 设置行颜色
            _materialGridView.RowsDefaultCellStyle.BackColor = Color.White;
            _materialGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.LightGray;

            Controls.Add(_materialGridView);
        }

        private void CreateButtonPanel()
        {
            _buttonPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                Padding = new Padding(10)
            };

            _addButton = new Button
            {
                Text = "新增",
                Size = new Size(75, 30),
                Location = new Point(10, 10)
            };
            _addButton.Click += AddButton_Click;

            _editButton = new Button
            {
                Text = "编辑",
                Size = new Size(75, 30),
                Location = new Point(95, 10)
            };
            _editButton.Click += EditButton_Click;

            _deleteButton = new Button
            {
                Text = "删除",
                Size = new Size(75, 30),
                Location = new Point(180, 10)
            };
            _deleteButton.Click += DeleteButton_Click;

            _refreshButton = new Button
            {
                Text = "刷新",
                Size = new Size(75, 30),
                Location = new Point(265, 10)
            };
            _refreshButton.Click += RefreshButton_Click;

            _buttonPanel.Controls.AddRange(new Control[]
            {
                _addButton, _editButton, _deleteButton, _refreshButton
            });

            Controls.Add(_buttonPanel);
        }

        private async void LoadDataAsync()
        {
            try
            {
                _logger.LogInformation("加载原料数据");
                
                var materials = await _materialService.GetAllMaterialsAsync();
                _materialGridView.DataSource = materials.ToList();

                _logger.LogInformation("原料数据加载完成，共 {Count} 条记录", materials.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载原料数据失败");
                MessageBox.Show($"加载数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SearchButton_Click(object? sender, EventArgs e)
        {
            try
            {
                _logger.LogInformation("执行原料查询");

                IEnumerable<Material> materials;

                if (_typeFilterComboBox.SelectedIndex > 0) // 不是"全部"
                {
                    var selectedType = (MaterialType)Enum.Parse(typeof(MaterialType), _typeFilterComboBox.SelectedItem.ToString()!);
                    materials = await _materialService.GetMaterialsByTypeAsync(selectedType);
                }
                else
                {
                    materials = await _materialService.GetAllMaterialsAsync();
                }

                // 按名称过滤
                if (!string.IsNullOrWhiteSpace(_nameFilterTextBox.Text))
                {
                    var nameFilter = _nameFilterTextBox.Text.Trim();
                    materials = materials.Where(m => m.Name.Contains(nameFilter, StringComparison.OrdinalIgnoreCase));
                }

                _materialGridView.DataSource = materials.ToList();

                _logger.LogInformation("查询完成，找到 {Count} 条记录", materials.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询原料数据失败");
                MessageBox.Show($"查询失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddButton_Click(object? sender, EventArgs e)
        {
            try
            {
                _logger.LogInformation("打开新增原料对话框");
                
                using var dialog = new MaterialEditDialog(_materialService);
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "新增原料失败");
                MessageBox.Show($"新增失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_materialGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("请选择要编辑的原料", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var selectedMaterial = (Material)_materialGridView.SelectedRows[0].DataBoundItem;
                _logger.LogInformation("打开编辑原料对话框，原料ID: {Id}", selectedMaterial.Id);

                using var dialog = new MaterialEditDialog(_materialService, selectedMaterial);
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "编辑原料失败");
                MessageBox.Show($"编辑失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteButton_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_materialGridView.SelectedRows.Count == 0)
                {
                    MessageBox.Show("请选择要删除的原料", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var selectedMaterial = (Material)_materialGridView.SelectedRows[0].DataBoundItem;
                
                var result = MessageBox.Show($"确定要删除原料 '{selectedMaterial.Name}' 吗？", 
                    "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _logger.LogInformation("删除原料，ID: {Id}", selectedMaterial.Id);
                    
                    var success = await _materialService.DeleteMaterialAsync(selectedMaterial.Id);
                    if (success)
                    {
                        MessageBox.Show("删除成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadDataAsync();
                    }
                    else
                    {
                        MessageBox.Show("删除失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除原料失败");
                MessageBox.Show($"删除失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            LoadDataAsync();
        }
    }

    /// <summary>
    /// 原料编辑对话框（简化版本）
    /// </summary>
    public class MaterialEditDialog : Form
    {
        private readonly IMaterialManagementService _materialService;
        private readonly Material? _material;

        public MaterialEditDialog(IMaterialManagementService materialService, Material? material = null)
        {
            _materialService = materialService;
            _material = material;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            Text = _material == null ? "新增原料" : "编辑原料";
            Size = new Size(400, 300);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;

            // 简化的编辑界面
            var label = new Label
            {
                Text = "原料编辑功能开发中...",
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            var buttonPanel = new Panel
            {
                Height = 40,
                Dock = DockStyle.Bottom
            };

            var okButton = new Button
            {
                Text = "确定",
                DialogResult = DialogResult.OK,
                Location = new Point(240, 8),
                Size = new Size(75, 23)
            };

            var cancelButton = new Button
            {
                Text = "取消",
                DialogResult = DialogResult.Cancel,
                Location = new Point(320, 8),
                Size = new Size(75, 23)
            };

            buttonPanel.Controls.AddRange(new Control[] { okButton, cancelButton });
            Controls.AddRange(new Control[] { label, buttonPanel });
        }
    }
}
