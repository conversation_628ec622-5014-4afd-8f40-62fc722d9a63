using System.ComponentModel.DataAnnotations;

namespace SinterOptimizationSystem.Core.Models
{
    /// <summary>
    /// 化学成分类型枚举
    /// </summary>
    public enum CompositionType
    {
        /// <summary>
        /// 全铁(TFe)
        /// </summary>
        TFe = 1,
        
        /// <summary>
        /// 氧化钙(CaO)
        /// </summary>
        CaO = 2,
        
        /// <summary>
        /// 二氧化硅(SiO2)
        /// </summary>
        SiO2 = 3,
        
        /// <summary>
        /// 氧化镁(MgO)
        /// </summary>
        MgO = 4,
        
        /// <summary>
        /// 三氧化二铝(Al2O3)
        /// </summary>
        Al2O3 = 5,
        
        /// <summary>
        /// 水分(H2O)
        /// </summary>
        H2O = 6,
        
        /// <summary>
        /// 烧损(Ig)
        /// </summary>
        Ig = 7,
        
        /// <summary>
        /// 硫(S)
        /// </summary>
        S = 8,
        
        /// <summary>
        /// 磷(P)
        /// </summary>
        P = 9,
        
        /// <summary>
        /// 钛(TiO2)
        /// </summary>
        TiO2 = 10,
        
        /// <summary>
        /// 锰(MnO)
        /// </summary>
        MnO = 11,
        
        /// <summary>
        /// 钾(K2O)
        /// </summary>
        K2O = 12,
        
        /// <summary>
        /// 钠(Na2O)
        /// </summary>
        Na2O = 13
    }

    /// <summary>
    /// 原料化学成分模型
    /// </summary>
    public class MaterialComposition : BaseEntity
    {
        /// <summary>
        /// 原料ID
        /// </summary>
        public int MaterialId { get; set; }

        /// <summary>
        /// 原料对象
        /// </summary>
        public virtual Material Material { get; set; } = null!;

        /// <summary>
        /// 化学成分类型
        /// </summary>
        public CompositionType CompositionType { get; set; }

        /// <summary>
        /// 含量值(%)
        /// </summary>
        [Range(0, 100)]
        public decimal Content { get; set; }

        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime TestDate { get; set; } = DateTime.Now;

        /// <summary>
        /// 检测批次号
        /// </summary>
        [MaxLength(50)]
        public string? BatchNumber { get; set; }

        /// <summary>
        /// 检测方法
        /// </summary>
        [MaxLength(100)]
        public string? TestMethod { get; set; }

        /// <summary>
        /// 检测人员
        /// </summary>
        [MaxLength(50)]
        public string? Tester { get; set; }

        /// <summary>
        /// 是否为当前有效值
        /// </summary>
        public bool IsCurrent { get; set; } = true;

        /// <summary>
        /// 标准偏差
        /// </summary>
        public decimal? StandardDeviation { get; set; }

        /// <summary>
        /// 置信度
        /// </summary>
        public decimal? Confidence { get; set; }
    }
}
