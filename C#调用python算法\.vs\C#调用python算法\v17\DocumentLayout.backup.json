{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\C#调用python算法\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\C#调用python算法\\src\\csharp\\SinterOptimization.Core\\SinterOptimization.Core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\csharp\\SinterOptimization.Core\\SinterOptimization.Core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\C#调用python算法\\开发文档.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:开发文档.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\C#调用python算法\\start-client.bat||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:start-client.bat||{3B902123-F8A7-4915-9F01-361F908088D0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "start-client.bat", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\C#调用python算法\\start-client.bat", "RelativeDocumentMoniker": "start-client.bat", "ToolTip": "C:\\Users\\<USER>\\Desktop\\C#调用python算法\\start-client.bat", "RelativeToolTip": "start-client.bat", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "00000000-0000-0000-0000-000000000000.000000|iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFjSURBVDhPY6AI/P//n7G1s+c/lEsWYILSRANbaysUmgHkgl+/fpGFQXqxuuDkyZMMEydOBNOEAIYBixYvZhARl2QIiohi4BeTYJg+cxZUBjtAMeDSpUsM1rb2DEoKcgwnDx9kUFOQZ7BxcQeL4wIoBuzfv5+BjZOTYe2aNQyRkZEM69auYWBjY2VYv3UHw4otuxl2HzzK8OPHD6hqCEAxwNHRkeHrt28MQcEhDMuXL2cIANLP33xgUPdNYdB3dGN4JajGcPrSVahqCEAxQE9Pj+HE4UMMV+89ZNC1tGW4dOcBw7kzZxn0lYQZLj/5zGCoLMrw9NU7qGoIwAjEhLhYhm9vXjDsWLOC4fe7lwxm+loMl+69ZdCV4WW4cO8Ng4meNlQlBLBAaRRgbm4OxiAA8vOPU2cZLj34xqCnpswgIyYFFocBDBegAw4ODgYXO2uGcB9XBh01JTAfGVCcFwYaMDAAABi9qE6NKlarAAAAAElFTkSuQmCC", "WhenOpened": "2025-07-28T06:26:16.789Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "SinterOptimization.Core.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\C#调用python算法\\src\\csharp\\SinterOptimization.Core\\SinterOptimization.Core.csproj", "RelativeDocumentMoniker": "src\\csharp\\SinterOptimization.Core\\SinterOptimization.Core.csproj", "ToolTip": "C:\\Users\\<USER>\\Desktop\\C#调用python算法\\src\\csharp\\SinterOptimization.Core\\SinterOptimization.Core.csproj", "RelativeToolTip": "src\\csharp\\SinterOptimization.Core\\SinterOptimization.Core.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-28T06:26:02.397Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "开发文档.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\C#调用python算法\\开发文档.md", "RelativeDocumentMoniker": "开发文档.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\C#调用python算法\\开发文档.md", "RelativeToolTip": "开发文档.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-28T06:20:43.028Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{1c64b9c2-e352-428e-a56d-0ace190b99a6}"}]}]}]}